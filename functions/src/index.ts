import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import axios from "axios";

// Initialize Firebase Admin SDK
admin.initializeApp();

// Types for API responses
interface TrainLocationData {
  train_number: string;
  date: string;
  stations: Array<{
    station_code: string;
    station_name: string;
    coaches: Array<{
      coach_number: string;
      onboarding_count: number;
      off_boarding_count: number;
      vacant_count: number;
    }>;
  }>;
}

interface NotificationPayload {
  title: string;
  body: string;
  data: {
    type: string;
    station_code: string;
    coach_data: string;
    timestamp: string;
  };
}

// Enhanced types for new functionality
interface UserCoachAssignment {
  out?: { [key: string]: string[] }; // outward journey coach assignments
  in?: { [key: string]: string[] };  // return journey coach assignments
  has_access: boolean;
}

interface StationNotificationState {
  station_code: string;
  coach_data: { [coach: string]: [number, number, number] }; // [onboarding, off_boarding, vacant]
  last_notified: string; // ISO timestamp
  notification_count: number;
}

/**
 * Get FCM token for a specific user from Firestore
 */
const getUserFcmToken = async (userId: string): Promise<string | null> => {
  try {
    const doc = await admin.firestore().collection("tokens").doc(userId).get();
    const data = doc.data();
    return data?.fcm_token || null;
  } catch (error) {
    console.error(`Error fetching FCM token for user ${userId}:`, error);
    return null;
  }
};

/**
 * Get user's coach assignments for a specific train and date
 */
const getUserCoachAssignments = async (
  userId: string,
  trainNumber: string,
  date: string,
  bearerToken: string
): Promise<UserCoachAssignment | null> => {
  try {
    const response = await axios.post(
      "https://railops-uat-api.biputri.com/api/users/get_Train_access_coach_wise/",
      {
        train_number: trainNumber,
        date: date,
        for_user_type: "coach attendent", // This might need to be dynamic based on user type
        token: bearerToken,
      },
      {
        headers: {
          "Authorization": `Bearer ${bearerToken}`,
          "Content-Type": "application/json",
        },
        timeout: 10000,
      }
    );

    console.log(`Coach assignments for user ${userId}:`, JSON.stringify(response.data, null, 2));
    return response.data as UserCoachAssignment;
  } catch (error) {
    console.error(`Error fetching coach assignments for user ${userId}:`, error);
    return null;
  }
};

// Legacy functions removed to clean up unused code
// calculateDistance, isAlertAlreadySent, markAlertAsSent functions
// have been replaced by enhanced station-level notification tracking

/**
 * Get station notification state from Firestore
 */
const getStationNotificationState = async (
  userId: string,
  trainNumber: string,
  date: string,
  stationCode: string
): Promise<StationNotificationState | null> => {
  try {
    const docId = `${userId}/${trainNumber}/${date}/${stationCode}`;
    const doc = await admin.firestore().collection("stationNotifications").doc(docId).get();

    if (doc.exists) {
      const data = doc.data();
      return {
        station_code: data?.station_code || stationCode,
        coach_data: data?.coach_data || {},
        last_notified: data?.last_notified || "",
        notification_count: data?.notification_count || 0,
      };
    }
    return null;
  } catch (error) {
    console.error(`Error getting station notification state for ${stationCode}:`, error);
    return null;
  }
};

/**
 * Check if station notification should be sent based on change detection
 */
const shouldSendStationNotification = async (
  userId: string,
  trainNumber: string,
  date: string,
  stationCode: string,
  currentCoachData: { [coach: string]: [number, number, number] }
): Promise<boolean> => {
  try {
    const existingState = await getStationNotificationState(userId, trainNumber, date, stationCode);

    // If no previous state, send notification
    if (!existingState) {
      console.log(`No previous state for station ${stationCode}, sending notification`);
      return true;
    }

    // Check if coach data has changed
    const hasChanged = Object.keys(currentCoachData).some(coach => {
      const currentData = currentCoachData[coach];
      const previousData = existingState.coach_data[coach];

      if (!previousData) return true; // New coach data

      return (
        currentData[0] !== previousData[0] || // onboarding count changed
        currentData[1] !== previousData[1] || // off_boarding count changed
        currentData[2] !== previousData[2]    // vacant count changed
      );
    });

    if (hasChanged) {
      console.log(`Coach data changed for station ${stationCode}, sending notification`);
      return true;
    }

    console.log(`No changes detected for station ${stationCode}, skipping notification`);
    return false;
  } catch (error) {
    console.error(`Error checking station notification state for ${stationCode}:`, error);
    return true; // Send notification on error to be safe
  }
};

/**
 * Update station notification state in Firestore
 */
const updateStationNotificationState = async (
  userId: string,
  trainNumber: string,
  date: string,
  stationCode: string,
  coachData: { [coach: string]: [number, number, number] }
): Promise<void> => {
  try {
    const docId = `${userId}/${trainNumber}/${date}/${stationCode}`;
    const existingState = await getStationNotificationState(userId, trainNumber, date, stationCode);

    await admin.firestore().collection("stationNotifications").doc(docId).set({
      station_code: stationCode,
      coach_data: coachData,
      last_notified: new Date().toISOString(),
      notification_count: (existingState?.notification_count || 0) + 1,
      updated_at: admin.firestore.FieldValue.serverTimestamp(),
    });
  } catch (error) {
    console.error(`Error updating station notification state for ${stationCode}:`, error);
  }
};

/**
 * Fetch train location data from microservice
 */
const fetchTrainLocationData = async (
  trainNumber: string,
  date: string,
  bearerToken: string
): Promise<TrainLocationData | null> => {
  try {
    const response = await axios.get(
      "https://railops-uat-api.biputri.com/microservice/train/location/",
      {
        params: {
          train_number: trainNumber,
          date: date,
        },
        headers: {
          "Authorization": `Bearer ${bearerToken}`,
          "Content-Type": "application/json",
        },
        timeout: 10000, // 10 second timeout
      }
    );

    // Log the actual API response for debugging
    console.log("Train location API response:", JSON.stringify(response.data, null, 2));

    // Validate response structure
    const data = response.data;
    if (!data || typeof data !== "object") {
      console.error("Invalid API response: data is not an object");
      return null;
    }

    // Transform the API response to match our expected format
    // API returns: { "BWN": { "D3": [], "S7": [] }, "NJP": { "C1": [] } }
    // We need: { stations: [{ station_code: "BWN", coaches: [...] }] }

    const transformedData: TrainLocationData = {
      train_number: trainNumber,
      date: date,
      stations: []
    };

    // Convert station data to our expected format
    for (const [stationCode, coachData] of Object.entries(data)) {
      if (typeof coachData === "object" && coachData !== null) {
        const coaches = [];

        // Convert coach data to our expected format
        for (const [coachNumber, passengerData] of Object.entries(coachData)) {
          if (Array.isArray(passengerData)) {
            coaches.push({
              coach_number: coachNumber,
              onboarding_count: passengerData.length > 0 ? passengerData[0] || 0 : 0,
              off_boarding_count: passengerData.length > 1 ? passengerData[1] || 0 : 0,
              vacant_count: passengerData.length > 2 ? passengerData[2] || 0 : 0,
            });
          } else {
            // Handle case where coach data is not an array
            coaches.push({
              coach_number: coachNumber,
              onboarding_count: 0,
              off_boarding_count: 0,
              vacant_count: 0,
            });
          }
        }

        transformedData.stations.push({
          station_code: stationCode,
          station_name: stationCode, // Use station code as name for now
          coaches: coaches
        });
      }
    }

    console.log("Transformed data:", JSON.stringify(transformedData, null, 2));
    return transformedData;
  } catch (error) {
    console.error("Error fetching train location data:", error);
    return null;
  }
};

/**
 * Filter coach data based on user assignments
 */
const filterCoachesByUserAssignment = (
  stations: TrainLocationData["stations"],
  userAssignments: UserCoachAssignment
): TrainLocationData["stations"] => {
  if (!userAssignments.has_access) {
    console.log("User has no coach access, returning empty stations");
    return [];
  }

  // Get all assigned coaches (combine outward and return journey assignments)
  const assignedCoaches = new Set<string>();

  if (userAssignments.out) {
    Object.values(userAssignments.out).forEach(coaches => {
      coaches.forEach(coach => assignedCoaches.add(coach));
    });
  }

  if (userAssignments.in) {
    Object.values(userAssignments.in).forEach(coaches => {
      coaches.forEach(coach => assignedCoaches.add(coach));
    });
  }

  console.log(`User assigned coaches: ${Array.from(assignedCoaches).join(", ")}`);

  // Filter stations to only include assigned coaches
  return stations.map(station => ({
    ...station,
    coaches: station.coaches.filter(coach => assignedCoaches.has(coach.coach_number))
  })).filter(station => station.coaches.length > 0); // Remove stations with no assigned coaches
};

/**
 * Build enhanced coach table string for notification body with table format
 */
const buildEnhancedCoachTable = (stations: TrainLocationData["stations"]): string => {
  // Validate stations array
  if (!Array.isArray(stations)) {
    console.error("buildEnhancedCoachTable: stations is not an array", stations);
    return "No station data available";
  }

  if (stations.length === 0) {
    console.log("buildEnhancedCoachTable: stations array is empty");
    return "No stations found";
  }

  // Check if there's any passenger activity
  const hasActivity = stations.some(station =>
    station.coaches.some(coach =>
      coach.onboarding_count > 0 || coach.off_boarding_count > 0 || coach.vacant_count > 0
    )
  );

  if (!hasActivity) {
    const stationCodes = stations.map(s => s.station_code).join(", ");
    return `No passenger onboarding/deboarding at station${stations.length > 1 ? "s" : ""} ${stationCodes}.`;
  }

  // Build table format: StationCode | Coach | Onboarding | De-boarding | Vacant
  let table = "Station | Coach | Board | Deboard | Vacant\n";
  table += "--------|-------|-------|---------|-------\n";

  stations.forEach(station => {
    station.coaches.forEach(coach => {
      if (coach.onboarding_count > 0 || coach.off_boarding_count > 0 || coach.vacant_count > 0) {
        table += `${station.station_code} | ${coach.coach_number} | ${coach.onboarding_count} | ${coach.off_boarding_count} | ${coach.vacant_count}\n`;
      }
    });
  });

  // Ensure the result is not too long for FCM (limit to ~300 characters)
  if (table.length > 300) {
    // Create a summary instead
    let totalOnboarding = 0;
    let totalOffBoarding = 0;
    let totalVacant = 0;
    let coachCount = 0;

    stations.forEach(station => {
      station.coaches.forEach(coach => {
        totalOnboarding += coach.onboarding_count;
        totalOffBoarding += coach.off_boarding_count;
        totalVacant += coach.vacant_count;
        coachCount++;
      });
    });

    const stationCodes = stations.map(s => s.station_code).join(", ");
    return `${stationCodes}: ${totalOnboarding} boarding, ${totalOffBoarding} deboarding, ${totalVacant} vacant (${coachCount} coaches)`;
  }

  return table;
};

// Legacy buildCoachTable function removed - using buildEnhancedCoachTable directly

/**
 * Send FCM notification with custom sound
 */
const sendFcmNotification = async (
  fcmToken: string,
  payload: NotificationPayload
): Promise<boolean> => {
  try {
    const message = {
      token: fcmToken,
      notification: {
        title: payload.title,
        body: payload.body,
      },
      data: payload.data,
      android: {
        notification: {
          channelId: "railops_alerts",
          sound: "railops_alarm",
          priority: "high" as const,
        },
      },
      apns: {
        payload: {
          aps: {
            sound: "railops_alarm.caf",
            category: "RAILOPS_ALERT",
          },
        },
      },
    };

    const response = await admin.messaging().send(message);
    console.log("FCM message sent successfully:", response);
    return true;
  } catch (error) {
    console.error("Error sending FCM message:", error);
    return false;
  }
};

/**
 * Main Cloud Function: POST /notify endpoint
 * Fetches train location data, builds coach tables, and sends FCM notifications
 */
export const notify = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  if (req.method === "OPTIONS") {
    res.status(200).send();
    return;
  }

  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed. Use POST." });
    return;
  }

  try {
    // Extract request parameters
    const { user_id, train_number, date, lat, lng } = req.body;

    // Validate required parameters
    if (!user_id || !train_number || !date) {
      res.status(400).json({
        error: "Missing required parameters: user_id, train_number, date",
      });
    }

    // Log coordinates if provided (for future proximity-based features)
    if (lat && lng) {
      console.log(`Request coordinates: lat=${lat}, lng=${lng}`);
    }

    console.log(`Processing notification request for user: ${user_id}, train: ${train_number}, date: ${date}`);

    // Log invocation details for monitoring
    console.log(`[NOTIFY] Invocation - User: ${user_id}, Train: ${train_number}, Date: ${date}, Coordinates: ${lat || "N/A"},${lng || "N/A"}`);

    // Get RAILOPS_BEARER secret
    const bearerToken = functions.config().railops?.bearer;
    if (!bearerToken) {
      console.error("RAILOPS_BEARER secret not configured");
      res.status(500).json({ error: "Server configuration error" });
      return;
    }

    // Get user's FCM token
    const fcmToken = await getUserFcmToken(user_id);
    if (!fcmToken) {
      console.log(`[NOTIFY] Status: SKIPPED, User: ${user_id}, Station: N/A - No FCM token found`);
      res.status(404).json({ error: "User FCM token not found" });
      return;
    }

    // Get user's coach assignments
    const userAssignments = await getUserCoachAssignments(user_id, train_number, date, bearerToken);
    if (!userAssignments || !userAssignments.has_access) {
      console.log(`[NOTIFY] Status: SKIPPED, User: ${user_id}, Station: N/A - User has no coach assignments for train ${train_number}`);
      res.status(200).json({
        status: "skipped",
        message: "User has no coach assignments for this train",
        user_id: user_id,
        train_number: train_number,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Fetch train location data
    const trainData = await fetchTrainLocationData(train_number, date, bearerToken);
    if (!trainData) {
      console.log(`[NOTIFY] Status: ERROR, User: ${user_id}, Station: N/A - Failed to fetch train location data for ${train_number}`);
      res.status(500).json({ error: "Failed to fetch train location data" });
      return;
    }

    // Filter stations to only include coaches assigned to the user
    const filteredStations = filterCoachesByUserAssignment(trainData.stations, userAssignments);

    if (filteredStations.length === 0) {
      console.log(`[NOTIFY] Status: SKIPPED, User: ${user_id}, Station: N/A - No assigned coaches found in train location data`);
      res.status(200).json({
        status: "skipped",
        message: "No assigned coaches found in current train location data",
        user_id: user_id,
        train_number: train_number,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Check station-level notifications and change detection
    const stationsToNotify = [];
    for (const station of filteredStations) {
      // Convert coach data to the format expected by shouldSendStationNotification
      const coachData: { [coach: string]: [number, number, number] } = {};
      station.coaches.forEach(coach => {
        coachData[coach.coach_number] = [
          coach.onboarding_count,
          coach.off_boarding_count,
          coach.vacant_count
        ];
      });

      const shouldNotify = await shouldSendStationNotification(
        user_id,
        train_number,
        date,
        station.station_code,
        coachData
      );

      if (shouldNotify) {
        stationsToNotify.push(station);
        // Update the notification state for this station
        await updateStationNotificationState(
          user_id,
          train_number,
          date,
          station.station_code,
          coachData
        );
      }
    }

    if (stationsToNotify.length === 0) {
      console.log(`[NOTIFY] Status: SKIPPED, User: ${user_id}, Station: N/A - No changes detected in coach data`);
      res.status(200).json({
        status: "skipped",
        message: "No changes detected in coach data",
        user_id: user_id,
        train_number: train_number,
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Build enhanced coach table for notification
    const coachTable = buildEnhancedCoachTable(stationsToNotify);

    // Create enhanced notification payload
    const stationCodes = stationsToNotify.map(s => s.station_code).join(", ");
    const notificationPayload: NotificationPayload = {
      title: `Train ${train_number} - Station Updates`,
      body: coachTable,
      data: {
        type: "enhanced_train_location_update",
        station_code: stationCodes,
        coach_data: JSON.stringify({
          stations_count: stationsToNotify.length,
          stations: stationsToNotify.map(s => s.station_code),
          summary: coachTable
        }),
        timestamp: new Date().toISOString(),
      },
    };

    // Send FCM notification
    const notificationSent = await sendFcmNotification(fcmToken, notificationPayload);
    if (!notificationSent) {
      console.log(`[NOTIFY] Status: ERROR, User: ${user_id}, Station: ${stationCodes} - Failed to send FCM notification`);
      res.status(500).json({ error: "Failed to send notification" });
      return;
    }

    console.log(`[NOTIFY] Status: SENT, User: ${user_id}, Station: ${stationCodes} - Enhanced notification sent successfully`);
    res.status(200).json({
      status: "sent",
      message: "Enhanced notification sent successfully",
      user_id: user_id,
      train_number: train_number,
      stations_notified: stationsToNotify.map(s => s.station_code),
      stations_count: stationsToNotify.length,
      total_coaches: stationsToNotify.reduce((sum, station) => sum + station.coaches.length, 0),
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error in notify function:", error);
    res.status(500).json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});
