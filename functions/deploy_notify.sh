#!/usr/bin/env bash
# Usage: ./deploy_notify.sh <RAILOPS_BEARER>
set -e

TOKEN="$1"
if [ -z "$TOKEN" ]; then
  echo "Usage: $0 <RAILOPS_BEARER>"
  echo ""
  echo "Example:"
  echo "  ./deploy_notify.sh \"Bearer your-api-token-here\""
  exit 1
fi

echo "🔐 Storing RAILOPS_BEARER secret..."
firebase functions:secrets:set RAILOPS_BEARER --data "$TOKEN" --project railwaysapp-prod

echo "🚀 Deploying functions..."
cd "$(dirname "$0")"
npm run deploy        # wraps: firebase deploy --only functions --project railwaysapp-prod

echo "✅ Deploy complete. Endpoint will be:"
echo "https://us-central1-railwaysapp-prod.cloudfunctions.net/notify"
echo ""
echo "🧪 Test with curl:"
echo 'curl -X POST https://us-central1-railwaysapp-prod.cloudfunctions.net/notify \'
echo '  -H "Content-Type: application/json" \'
echo '  -d '"'"'{"user_id": "test123", "train_number": "12345", "date": "2024-12-20", "lat": "28.6139", "lng": "77.2090"}'"'"
