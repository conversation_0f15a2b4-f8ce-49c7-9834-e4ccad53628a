#!/bin/bash

# RailOps Firebase Functions Deployment Script
# This script handles the complete deployment process for Firebase Cloud Functions

set -e  # Exit on any error

echo "🚀 Starting RailOps Firebase Functions Deployment..."

# Check if we're in the functions directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the functions/ directory"
    exit 1
fi

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Error: Firebase CLI is not installed"
    echo "Install it with: npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in to Firebase
if ! firebase projects:list &> /dev/null; then
    echo "🔐 Please log in to Firebase..."
    firebase login
fi

# Set the correct Firebase project
echo "📋 Setting Firebase project to railwaysapp-prod..."
firebase use railwaysapp-prod

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Run linting
echo "🔍 Running ESLint..."
npm run lint

# Build TypeScript
echo "🔨 Building TypeScript..."
npm run build

# Check if RAILOPS_BEARER secret is set
echo "🔑 Checking RAILOPS_BEARER secret..."
if ! firebase functions:secrets:access RAILOPS_BEARER &> /dev/null; then
    echo "⚠️  RAILOPS_BEARER secret not found!"
    echo "Please set it with: firebase functions:secrets:set RAILOPS_BEARER"
    read -p "Do you want to set it now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        firebase functions:secrets:set RAILOPS_BEARER
    else
        echo "❌ Deployment cancelled. Please set RAILOPS_BEARER secret first."
        exit 1
    fi
fi

# Deploy functions
echo "🚀 Deploying Firebase Functions..."
firebase deploy --only functions

# Deploy Firestore rules and indexes
echo "📊 Deploying Firestore rules and indexes..."
firebase deploy --only firestore

echo "✅ Deployment completed successfully!"
echo ""
echo "🔗 Your function is available at:"
echo "https://us-central1-railwaysapp-prod.cloudfunctions.net/notify"
echo ""
echo "📝 Test with curl:"
echo 'curl -X POST https://us-central1-railwaysapp-prod.cloudfunctions.net/notify \'
echo '  -H "Content-Type: application/json" \'
echo '  -d '"'"'{"user_id": "test123", "train_number": "12345", "date": "2024-12-20"}'"'"
echo ""
echo "📊 Monitor logs with: firebase functions:log"
