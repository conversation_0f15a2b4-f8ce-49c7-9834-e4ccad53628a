import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:railops/core/utilities/color_constants.dart';
import 'package:railops/models/index.dart';
import 'package:railops/screens/pdf_screen/widgets/date_select.dart';
import 'package:railops/screens/pdf_screen/widgets/pdf_buttons.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/depot_dropdown.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/division_dropdown.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/train_number_dropdown.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/zone_dropdown.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/services/train_services/index.dart';
import 'package:railops/types/edit_train_types/edit_train_types.dart';
import 'package:railops/types/train_types/train_charting_response.dart';
import 'package:railops/types/train_types/zone_division_type.dart';
import 'package:railops/widgets/index.dart';

class PdfScreen extends StatefulWidget {
  const PdfScreen({super.key});

  @override
  State<PdfScreen> createState() => _PdfScreenState();
}

class _PdfScreenState extends State<PdfScreen> {
  DateTime _selectedDate = DateTime.now();
  String _division = '', _depot = '', _selectedZones = '';
  List<String> depots = [];
  List<String> trainList = [];
  List<String> _trainNumbers = [];
  List<String> division_codes = [];
  String? selectedTrain;
  bool showAll = false;
  bool showAllMonthly = false;
  String depot = '';
  List<int> frequency = [];
  String? trainScheduledAt = "NA";
  List? refreshedTimes = [];
  String? expectedChartingTime = "NA";
  String? loadedAtEarliest = "NA";
  String? loadedAtRecent = "NA";
  bool isRunningDay = false;
  List<String> stations = [];
  String startTime = "NA";
  String endTime = "NA";
  EditTrainsData? trainData;
  TrainChartingResponse? chartData;

  // New variables to store multiple train details
  Map<String, EditTrainsData> trainDetailsMap = {};
  Map<String, TrainChartingResponse> chartDataMap = {};
  bool isLoadingTrainDetails = false;

  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    String storedDate =
        Provider.of<UserModel>(context, listen: false).selectedDate;
    if (storedDate.isNotEmpty) {
      _selectedDate = DateTime.parse(storedDate);
    }
  }

  void _handleDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
    Provider.of<UserModel>(context, listen: false).setSelectedDate(
        "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}");

    // Fetch charting time for all selected trains when date changes
    if (_trainNumbers.isNotEmpty) {
      _fetchAllTrainDetails();
    }
  }

  void _handleZoneSelection(String selectedZones) async {
    setState(() {
      _selectedZones = selectedZones;
    });

    if (_selectedZones.isNotEmpty) {
      try {
        List<ZoneDivision> divisions =
            await TrainServiceSignup.getDivisions(_selectedZones);
        List<String> divisionCodes =
            divisions.map((division) => division.code).toList();
        setState(() {
          division_codes = divisionCodes;
        });
      } catch (e) {
        print("Error fetching divisions: $e");
      }
    }
  }

  Future getDepot() async {
    try {
      if (_division.isEmpty) return;
      final getData = await TrainServiceSignup.getDepot(_division);
      setState(() {
        depots = getData;
      });
    } catch (e) {
      showErrorModal(context, '$e', "Error", () {});
    }
  }

  Future getTrainList() async {
    try {
      final getData = await TrainServiceSignup.getTrainList(_depot);
      setState(() {
        trainList = getData['trains'];
      });
    } catch (e) {
      showErrorModal(context, '$e', "Error", () {});
    }
  }

  void _showSelectAllTrainsError() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.blue,
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.black, size: 28),
                  SizedBox(width: 8),
                  Text(
                    "Info",
                    style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              IconButton(
                icon: const Icon(
                  Icons.close,
                  color: Colors.black,
                  size: 24,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
          titlePadding: const EdgeInsets.fromLTRB(24, 20, 16, 0),
          content: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
            ),
            child: const Text(
              "Please select only relevant Trains to generate report. The more the trains the more time it will take to generate the report.",
              style: TextStyle(
                color: Colors.black,
                fontSize: 15.0,
              ),
            ),
          ),
          // Removed the actions section completely
        );
      },
    );
  }
  // Updated method to handle multiple train numbers and fetch details
  void _handleTrainNumbersSelection(List<String> selectedTrainNumbers) {
    if (selectedTrainNumbers.length == trainList.length &&
        trainList.isNotEmpty) {
      _showSelectAllTrainsError();
      return;
    }

    // Check if "Select All" option is explicitly selected (if your dropdown has this option)
    if (selectedTrainNumbers.contains("Select All") ||
        selectedTrainNumbers.contains("ALL") ||
        selectedTrainNumbers.contains("All Trains")) {
      _showSelectAllTrainsError();
      return;
    }

    setState(() {
      _trainNumbers = selectedTrainNumbers;
    });

    // Fetch details for all selected trains
    if (_trainNumbers.isNotEmpty) {
      _fetchAllTrainDetails();
    } else {
      // Clear data when no trains are selected
      setState(() {
        trainDetailsMap.clear();
        chartDataMap.clear();
        selectedTrain = null;
        trainData = null;
        chartData = null;
        startTime = "NA";
        endTime = "NA";
        frequency = [];
        trainScheduledAt = "NA";
        refreshedTimes = [];
        expectedChartingTime = "NA";
        loadedAtEarliest = "NA";
        loadedAtRecent = "NA";
        isRunningDay = false;
      });
    }
  }

  // Updated method to fetch details for all selected trains
  Future<void> _fetchAllTrainDetails() async {
    if (_trainNumbers.isEmpty) return;

    setState(() {
      isLoadingTrainDetails = true;
    });

    try {
      // Clear previous data
      trainDetailsMap.clear();
      chartDataMap.clear();

      // Fetch train details and charting data for each selected train
      for (String trainNo in _trainNumbers) {
        // Fetch train details
        await _fetchTrainDetails(trainNo);

        // Fetch charting time for each train
        await _fetchChartingTimeForTrain(trainNo);
      }

      // Handle single train selection for backward compatibility
      if (_trainNumbers.length == 1) {
        selectedTrain = _trainNumbers.first;
        if (trainDetailsMap.containsKey(selectedTrain!)) {
          final trainDetail = trainDetailsMap[selectedTrain!]!;
          setState(() {
            startTime = trainDetail.startTime ?? 'NA';
            endTime = trainDetail.endTime ?? 'NA';
            trainData = trainDetail;
            frequency = trainDetail.frequency ?? [];
          });
        }

        if (chartDataMap.containsKey(selectedTrain!)) {
          final chartDetail = chartDataMap[selectedTrain!]!;
          setState(() {
            chartData = chartDetail;
            trainScheduledAt = chartDetail.startTime;
            refreshedTimes = chartDetail.refreshedTimes;
            expectedChartingTime = chartDetail.chartingTime;
            loadedAtEarliest = chartDetail.loadedAtEarliest;
            loadedAtRecent = chartDetail.loadedAtRecent;
            isRunningDay = chartDetail.isRunningDay;
          });
          // Show status modal for single train with correct parameters
          _showTrainStatusModal(selectedTrain!, isRunningDay, frequency);
        }
      } else if (_trainNumbers.length > 1) {
        // Handle multiple train selection - show status for all non-running trains
        _showMultipleTrainStatusModal();
      }
    } catch (e) {
      print('Error fetching train details: $e');
    } finally {
      setState(() {
        isLoadingTrainDetails = false;
      });
    }
  }

  // Updated method to fetch train details and store in map
  Future<void> _fetchTrainDetails(String trainNo) async {
    try {
      final trainDetails =
          await EditTrainServices.fetchTrainDetailsByTrainNo(trainNo);
      if (trainDetails != null) {
        setState(() {
          trainDetailsMap[trainNo] = trainDetails;
        });

        // If this is the only selected train, update the main variables
        if (_trainNumbers.length == 1 && _trainNumbers.first == trainNo) {
          setState(() {
            startTime = trainDetails.startTime ?? 'NA';
            endTime = trainDetails.endTime ?? 'NA';
            trainData = trainDetails;
            frequency = trainDetails.frequency ?? [];
          });
        }
      }
    } catch (e) {
      print('Error fetching train details for $trainNo: $e');
    }
  }

  // New method to fetch charting time for a specific train
  Future<void> _fetchChartingTimeForTrain(String trainNo) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;

    try {
      final chartResponse = await TrainService.fetchChartingTime(
          trainNumber: trainNo,
          date: DateFormat('yyyy-MM-dd').format(_selectedDate),
          token: userToken);

      if (chartResponse != null) {
        setState(() {
          chartDataMap[trainNo] = chartResponse;
        });

        // If this is the only selected train, update the main variables
        if (_trainNumbers.length == 1 && _trainNumbers.first == trainNo) {
          setState(() {
            chartData = chartResponse;
            trainScheduledAt = chartResponse.startTime;
            refreshedTimes = chartResponse.refreshedTimes;
            expectedChartingTime = chartResponse.chartingTime;
            loadedAtEarliest = chartResponse.loadedAtEarliest;
            loadedAtRecent = chartResponse.loadedAtRecent;
            isRunningDay = chartResponse.isRunningDay;
          });
        }
      }
    } catch (e) {
      print('Error fetching charting time for $trainNo: $e');
    }
  }

  // Keep the original method for backward compatibility
  void fetchChartingTime() async {
    if (selectedTrain == null) return;
    await _fetchChartingTimeForTrain(selectedTrain!);
  }

  String _formatRunningDays() {
    if (frequency.isEmpty) return 'N/A';

    final dayAbbreviations = frequency.map((dayIndex) {
      final dayName =
          DateFormat('EEEE').format(DateTime(2023, 1, 1 + dayIndex));
      return dayName.substring(0, 3);
    }).toList();

    if (dayAbbreviations.length == 7) return 'Daily';

    return dayAbbreviations.join(', ');
  }

  // Updated method to show multiple train status modal with cross icon close
  void _showMultipleTrainStatusModal() {
    String dayOfWeek = DateFormat('EEEE').format(_selectedDate);
    List<String> nonRunningTrains = [];
    List<String> runningTrains = [];

    // Separate running and non-running trains
    for (String trainNo in _trainNumbers) {
      if (chartDataMap.containsKey(trainNo)) {
        bool isRunning = chartDataMap[trainNo]!.isRunningDay;
        if (isRunning) {
          runningTrains.add(trainNo);
        } else {
          nonRunningTrains.add(trainNo);
        }
      } else {
        // If no chart data available, assume it's running
        runningTrains.add(trainNo);
      }
    }

    // Show modal only if there are non-running trains
    if (nonRunningTrains.isNotEmpty) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.red,
            title: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  "Trains Not Running",
                  style: TextStyle(
                      color: Colors.black,
                      fontSize: 16.0,
                      fontWeight: FontWeight.bold),
                ),
                IconButton(
                  icon: const Icon(
                    Icons.close,
                    color: Colors.black,
                    size: 24,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            titlePadding: const EdgeInsets.fromLTRB(24, 20, 16, 0),
            content: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height *
                    0.6, // Limit height to 60% of screen
                maxWidth: MediaQuery.of(context).size.width *
                    0.8, // Limit width to 80% of screen
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "The following trains are NOT running on *$dayOfWeek*:",
                      style:
                          const TextStyle(color: Colors.black, fontSize: 15.0),
                    ),
                    const SizedBox(height: 10),
                    ...nonRunningTrains.map((trainNo) {
                      String runningDays = 'N/A';
                      if (trainDetailsMap.containsKey(trainNo)) {
                        final frequency =
                            trainDetailsMap[trainNo]!.frequency ?? [];
                        if (frequency.isNotEmpty) {
                          final dayAbbreviations = frequency.map((dayIndex) {
                            final dayName = DateFormat('EEEE')
                                .format(DateTime(2023, 1, 1 + dayIndex));
                            return dayName.substring(0,3);
                          }).toList();
                          runningDays = dayAbbreviations.length == 7
                              ? 'Daily'
                              : dayAbbreviations.join(', ');
                        }
                      }

                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Container(
                          padding: const EdgeInsets.all(8.0),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Train: $trainNo",
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontSize: 15.0,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                "Running Days: $runningDays",
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontSize: 15.0,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                    if (runningTrains.isNotEmpty) ...[
                      const SizedBox(height: 15),
                      Text(
                        "Running trains on *$dayOfWeek* (${runningTrains.length}): ${runningTrains.join(', ')}",
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 15.0,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            // Removed the actions section completely
          );
        },
      );
    }
  }

  // Updated _showTrainStatusModal method with scrollable content
  void _showTrainStatusModal(
      String trainNo, bool isRunning, List<int> trainFrequency) {
    String dayOfWeek = DateFormat('EEEE').format(_selectedDate);
    if (!isRunning) {
      // Format running days for this specific train
      String runningDays = 'N/A';
      if (trainFrequency.isNotEmpty) {
        final dayAbbreviations = trainFrequency.map((dayIndex) {
          final dayName =
              DateFormat('EEEE').format(DateTime(2023, 1, 1 + dayIndex));
          return dayName.substring(0, 3);
        }).toList();
        runningDays = dayAbbreviations.length == 7
            ? 'Daily'
            : dayAbbreviations.join(', ');
      }

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.red,
            title: const Text(
              "Train Not Running",
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 16.0,
                  fontWeight: FontWeight.bold),
            ),
            content: Text(
              "Train *$selectedTrain* is *NOT* running on *$dayOfWeek*\nRunning Days: ${_formatRunningDays()}",
              style: const TextStyle(color: Colors.black, fontSize: 15.0),
            ),
            actions: <Widget>[
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade100,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'OK',
                  style: TextStyle(
                      fontSize: 14.0,
                      color: Colors.blue,
                      fontWeight: FontWeight.bold),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                  // Optional: Add any additional logic you want to execute when the modal is closed
                },
              ),
            ],
          );
        },
      );
    }
  }

  Future<void> _reloadPage() async {
    await Future.delayed(const Duration(seconds: 3));
    reloadCurrentScreen(context, const PdfScreen());
  }

  void reloadCurrentScreen(BuildContext context, Widget currentScreen) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => currentScreen,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'Reports'),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: _reloadPage,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Card(
                surfaceTintColor: kWhiteColor,
                child: Column(children: [
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: DateSelect(
                      onDateSelected: _handleDateSelected,
                      initialDate: _selectedDate,
                    ),
                  ),

                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: ZoneDropdown(
                      onSaved: _handleZoneSelection,
                    ),
                  ),

                  // Division dropdown
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: DivisionDropdown(
                      divisions: division_codes
                        ..sort((a, b) =>
                            a.toLowerCase().compareTo(b.toLowerCase())),
                      onSaved: (value) {
                        setState(() {
                          _division = value;
                          _depot = '';
                        });
                        getDepot();
                      },
                    ),
                  ),
                  // Depot dropdown
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: DepotDropdown(
                      depots: depots
                        ..sort(
                          (a, b) => a.toLowerCase().compareTo(b.toLowerCase()),
                        ),
                      onSaved: (value) {
                        setState(() {
                          _depot = value;
                        });
                        getTrainList();
                      },
                    ),
                  ),

                  // Train number dropdown
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: TrainNumberDropdown(
                      trainList: trainList,
                      onSaved: _handleTrainNumbersSelection,
                    ),
                  ),

                  // Loading indicator for train details
                  if (isLoadingTrainDetails)
                    const Padding(
                      padding: EdgeInsets.all(10.0),
                      child: CircularProgressIndicator(),
                    ),
                ]),
              ),
              // if (_trainNumbers.isNotEmpty)
              //     Padding(
              //       padding: const EdgeInsets.all(10.0),
              //       child: Card(
              //         color: Colors.blue.shade50,
              //         child: const Padding(
              //           padding: EdgeInsets.all(12.0),
              //           child: Column(
              //             crossAxisAlignment: CrossAxisAlignment.start,
              //             // children: [
              //             //   const Text(
              //             //     "Selected Trains:",
              //             //     style: TextStyle(
              //             //       fontWeight: FontWeight.bold,
              //             //       fontSize: 14,
              //             //     ),
              //             //   ),
              //             //   const SizedBox(height: 8),
              //             //   Wrap(
              //             //     spacing: 8,
              //             //     runSpacing: 4,
              //             //     children: _trainNumbers.map((trainNo) {
              //             //       final isRunning = chartDataMap.containsKey(trainNo)
              //             //           ? chartDataMap[trainNo]!.isRunningDay
              //             //           : true;
              //             //       return Container(
              //             //         padding: const EdgeInsets.symmetric(
              //             //           horizontal: 8,
              //             //           vertical: 4,
              //             //         ),
              //             //         decoration: BoxDecoration(
              //             //           color: isRunning ? Colors.green.shade100 : Colors.red.shade100,
              //             //           borderRadius: BorderRadius.circular(12),
              //             //           border: Border.all(
              //             //             color: isRunning ? Colors.green : Colors.red,
              //             //             width: 1,
              //             //           ),
              //             //         ),
              //             //         child: Text(
              //             //           trainNo,
              //             //           style: TextStyle(
              //             //             fontSize: 12,
              //             //             color: isRunning ? Colors.green.shade800 : Colors.red.shade800,
              //             //             fontWeight: FontWeight.w500,
              //             //           ),
              //             //         ),
              //             //       );
              //             //     }).toList(),
              //             //   ),
              //             // ],
              //           ),
              //         ),
              //       ),
              //     ),
              const SizedBox(height: 20),
              Card(
                surfaceTintColor: kWhiteColor,
                child: Container(
                  constraints: BoxConstraints(minHeight: showAll ? 300 : 200),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "Daily Reports",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        // Replace GridView.count with GridView.builder with adjusted aspect ratio
                        GridView.builder(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                            childAspectRatio:
                                1, // Adjusted to make cells taller
                          ),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(10),
                          itemCount: showAll ? 6 : 2,
                          itemBuilder: (context, index) {
                            // Define all buttons in an array
                            final buttons = [
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Attendance Report",
                                  urlLink: "/pdf/attendance_report/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.assignment,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/attendance_report.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Round Rake Deficiency Report",
                                  urlLink: "/pdf/round_trip_report/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.cached,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImageList: const [
                                    'assets/images/round_trip/1.png',
                                    'assets/images/round_trip/2.png',
                                    'assets/images/round_trip/3.png',
                                    'assets/images/round_trip/4.png',
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Train Report",
                                  urlLink: "/pdf/all_train_report/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.directions_transit,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/train_report.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Detailed Attendance Report",
                                  urlLink:
                                      "/pdf/get_detailed_attendance_by_date/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.assignment_returned_rounded,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImageList: const [
                                    'assets/images/attendance/1.png',
                                    'assets/images/attendance/2.png',
                                    'assets/images/attendance/3.png',
                                    'assets/images/attendance/4.png',
                                    'assets/images/attendance/5.png',
                                    'assets/images/attendance/6.png',
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Rake Deficiency Report",
                                  urlLink: "/pdf/trip_report/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.swap_horiz,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/trip_report.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText:
                                      "Detailed Round Trip Attendance Report",
                                  urlLink: "/pdf/detailed_round_trip_report/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.library_books,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImageList: const [
                                    'assets/images/round_trip/1.png',
                                    'assets/images/round_trip/2.png',
                                    'assets/images/round_trip/3.png',
                                    'assets/images/round_trip/4.png',
                                  ],
                                ),
                              ),
                            ];
                            return buttons[index];
                          },
                        ),
                        if (!showAll)
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  showAll = true;
                                });
                              },
                              icon: const Icon(Icons.expand_more),
                              label: const Text("Show More"),
                            ),
                          ),
                        if (showAll)
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  showAll = false;
                                });
                              },
                              icon: const Icon(Icons.expand_less),
                              label: const Text("Show Less"),
                            ),
                          )
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Card(
                surfaceTintColor: kWhiteColor,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Container(
                    constraints:
                        BoxConstraints(minHeight: showAllMonthly ? 200 : 150),
                    child: Column(
                      children: [
                        const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "Monthly Reports",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        GridView.builder(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                            childAspectRatio:
                                0.95, // Adjusted to make cells taller
                          ),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(10.0),
                          itemCount: showAllMonthly ? 4 : 2,
                          itemBuilder: (context, index) {
                            final buttons = [
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Monthly Attendance Report",
                                  urlLink:
                                      "/pdf/get_monthly_attendance_report/",
                                  isMail: false,
                                  directMail: true,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.assignment,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/attendance_report.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText:
                                      "Monthly Round Trip Attendance Report",
                                  urlLink: "/pdf/monthly_round_trip_report/",
                                  isMail: false,
                                  directMail: true,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.cached,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/attendance_report.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Monthly With Mobile",
                                  urlLink: "/pdf/monthly_obhs_report_links/",
                                  isMail: true,
                                  directMail: true,
                                  trainNumbers: _trainNumbers,
                                  showPhoneColumn: true,
                                  division: _division,
                                  showDownloadButtonForMonthly: false,
                                  buttonIcon: const Icon(
                                    Icons.smartphone,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/monthly_report_with_mobile.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Monthly Without Mobile",
                                  urlLink: "/pdf/monthly_obhs_report_links/",
                                  isMail: true,
                                  directMail: true,
                                  trainNumbers: _trainNumbers,
                                  showPhoneColumn: false,
                                  division: _division,
                                  showDownloadButtonForMonthly: false,
                                  buttonIcon: const Icon(
                                    Icons.no_cell,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/monthly_report_without_phone.png",
                                ),
                              ),
                            ];
                            return buttons[index];
                          },
                        ),
                        if (!showAllMonthly)
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  showAllMonthly = true;
                                });
                              },
                              icon: const Icon(Icons.expand_more),
                              label: const Text("Show More"),
                            ),
                          ),
                        if (showAllMonthly)
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  showAllMonthly = false;
                                });
                              },
                              icon: const Icon(Icons.expand_less),
                              label: const Text("Show Less"),
                            ),
                          )
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
