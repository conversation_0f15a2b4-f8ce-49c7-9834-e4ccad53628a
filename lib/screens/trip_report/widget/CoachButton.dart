import 'package:flutter/material.dart';

class CoachButton extends StatelessWidget {
  final String coach;
  final List<int> issues;
  final VoidCallback onTap;
  final Map<int, String> problemOptions;
  final bool isLast;

  const CoachButton({
    Key? key,
    required this.coach,
    required this.issues,
    required this.onTap,
    required this.problemOptions,
    this.isLast = false,
  }) : super(key: key);

  IconData getIconForProblem(int problemId) {
    switch (problemId) {
      case 1:
        return Icons.door_front_door;
      case 2:
        return Icons.wash;
      case 3:
        return Icons.lock_outline;
      case 4:
        return Icons.plumbing_sharp;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    List<int> displayedIssues = issues.take(2).toList();

    return Container(
      // margin: const EdgeInsets.only(bottom: 5),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: onTap,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Image.asset(
                    'assets/images/icons8-railway-car-48.png', // coach image path
                    width: 80,
                    height: 80,
                    fit: BoxFit.contain,
                    ),
                    Text(
                      coach,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            offset: Offset(0.5, 0.5),
                            blurRadius: 1,
                            color: Colors.black,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              if (!isLast) const Icon(Icons.keyboard_tab, size: 20),
            ],
          ),
        ],
      ),
    );
  }
}
