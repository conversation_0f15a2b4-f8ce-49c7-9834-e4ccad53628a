import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_filters.dart';
import 'package:railops/screens/trip_report/widget/CoachButton.dart';
import 'package:railops/screens/trip_report/widget/CoachIssueImageUpload.dart';
import 'package:railops/screens/trip_report/widget/CoachIssueStatus.dart';
import 'package:railops/services/trip_report_services/trip_report_services.dart';
import 'package:railops/widgets/index.dart';

class TripReportScreen extends StatefulWidget {
  const TripReportScreen({Key? key}) : super(key: key);

  @override
  _TripReportScreenState createState() => _TripReportScreenState();
}

class _TripReportScreenState extends State<TripReportScreen> {
  String? train;
  String? date;
  bool showLoader = false;
  List<String> coaches = [];
  Map<String, List<int>> selectedProblems = {};
  Map<int, String> problemOptions = {};
  bool isInitialLoad = true;
  List<dynamic> allIssues = [];

  @override
  void initState() {
    super.initState();
    _loadAllIssues();
  }

  Future<void> _loadAllIssues() async {
    setState(() {
      showLoader = true;
    });

    try {
      List<dynamic> response = await TripReportServices.getAllIssues();
      setState(() {
        allIssues = response;
        problemOptions = {
          for (var issue in response) issue["id"]: issue["name"]
        };
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Failed to load issues: ${e.toString()}")),
      );
    } finally {
      setState(() {
        showLoader = false;
      });
    }
  }


  void onSubmit(String trainNumber, String selectedDate) async {
    setState(() {
      train = trainNumber;
      date = selectedDate;
      showLoader = true;
    });

    try {
      final response =
          await TripReportServices.getTripReport(trainNumber, selectedDate);
      if (response.containsKey('data') &&
          response['data'].containsKey('coach_issues')) {
        setState(() {
          coaches = List<String>.from(response['data']['coach_issues'].keys);

          // Only replace selectedProblems on initial load
          if (isInitialLoad) {
            selectedProblems = {};
            response['data']['coach_issues'].forEach((coach, issues) {
              selectedProblems[coach] =
                  List<int>.from((issues as List).map((e) => e['id'] as int));
            });
            isInitialLoad = false;
          } else {
            // Update coaches list but preserve selected problems
            coaches.forEach((coach) {
              if (!selectedProblems.containsKey(coach)) {
                selectedProblems[coach] = List<int>.from(
                    (response['data']['coach_issues'][coach] as List)
                        .map((e) => e['id'] as int));
              }
            });
          }
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Invalid response format from server")),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Failed to load trip report: ${e.toString()}")),
      );
    } finally {
      setState(() {
        showLoader = false;
      });
    }
  }

  Future<void> _reloadPage() async {
    if (train != null && date != null) {
      setState(() {
        showLoader = true;
      });

      try {
        onSubmit(train!, date!);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Data refreshed successfully")),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Refresh failed: ${e.toString()}")),
        );
      } finally {
        setState(() {
          showLoader = false;
        });
      }
    }
  }

  IconData getIconForProblem(int problemId) {
    print(problemId);
    switch (problemId) {
      case 1:
        return Icons.door_front_door;
      case 2:
        return Icons.wash;
      case 3:
        return Icons.lock_outline;
      case 4:
        return Icons.plumbing_sharp;
      default:
        return Icons.help_outline;
    }
  }


  Widget _buildCoachList() {
    if (coaches.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "No coaches found.",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              "Please select a train and date.",
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: coaches.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        String coach = coaches[index];
        List<int> issueIds = selectedProblems[coach] ?? [];

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Coach $coach",
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      issueIds.isEmpty
                          ? const Text(
                              "No issues reported",
                              style: TextStyle(
                                color: Colors.green,
                                fontStyle: FontStyle.italic,
                              ),
                            )
                          : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  "Reported Issues:",
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                ...issueIds
                                    .map((id) => Padding(
                                          padding:
                                              const EdgeInsets.symmetric(vertical: 4),
                                          child: Row(
                                            children: [
                                              const Icon(Icons.error_outline,
                                                  color: Colors.orange,
                                                  size: 16),
                                              const SizedBox(width: 8),
                                              Text(problemOptions[id] ??
                                                  "Unknown Issue"),
                                            ],
                                          ),
                                        ))
                                    .toList(),
                              ],
                            ),
                      IconButton(
                        iconSize: 40,
                        icon: const Icon(Icons.upload, color: Colors.blue),
                        onPressed: () => _handleImageUpload(coach),
                        tooltip: 'Upload Coach Image',
                      ),
                    ]),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _handleImageUpload(coach),
                  icon: const Icon(Icons.edit),
                  label: const Text("Manage Issues"),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleImageUpload(String coach) async {
    if (train == null || date == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please select a train and date first")),
      );
      return;
    }

    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CoachIssueImageUploadPage(
          trainNumber: train!,
          journeyDate: date!,
          coach: coach,
          problems: problemOptions.entries
              .where((entry) => selectedProblems[coach]?.contains(entry.key) ?? false)
              .map((entry) =>
                  {'id': entry.key.toString(), 'problemName': entry.value})
              .toList(),
        ),
      ),
    );

    // Mark as not initial load to preserve selected problems
    isInitialLoad = false;

    // Refresh data after returning from image upload
    if (train != null && date != null) {
      onSubmit(train!, date!);
    }
  }

  Widget _buildCoachGrid() {
    if (coaches.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "No coaches found.",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              "Please select a train and date.",
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 2.0,
      ),
      itemCount: coaches.length,
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        String coach = coaches[index];
        List<int> issueIds = selectedProblems[coach] ?? [];

        return CoachButton(
            coach: coach,
            issues: issueIds,
            // hasImages: coachHasImages[coach] ?? false,
            onTap: () => _handleImageUpload(coach),
            // onIssueEdit: () => _showProblemSelectionDialog(coach),
            problemOptions: problemOptions,
            isLast: index == coaches.length - 1);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'Train Rake Deficiency Report'),
      drawer: const CustomDrawer(),
      body: Column(
        children: [
          const Padding(
            padding: EdgeInsets.only(top: 5),
            child: Text("Rake Deficiency Report Issues",
                style: TextStyle(fontSize: 18)),
          ),
          Padding(
            padding: const EdgeInsets.all(5),
            child: AssignEhkCaFilters(
              onSubmit: (trainNumber, selectedDate) {
                if (trainNumber != null && selectedDate != null) {
                  setState(() {
                    // Reset for new train/date selection
                    isInitialLoad = true;
                  });
                  onSubmit(trainNumber, selectedDate);
                }
              },
            ),
          ),
          Expanded(
            child: showLoader
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                    onRefresh: _reloadPage,
                    child: _buildCoachGrid(),
                  ),
          ),
        ],
      ),
    );
  }
}
