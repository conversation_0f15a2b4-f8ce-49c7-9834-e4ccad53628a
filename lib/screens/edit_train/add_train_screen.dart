import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/assign_ehk_ca_screen/widgets/return_gap.dart';
import 'package:railops/screens/edit_train/edit_train_screen.dart';
import 'package:railops/screens/edit_train/widgets/coaches_input_field.dart';
import 'package:railops/screens/edit_train/widgets/coach_in_sequence.dart';
import 'package:railops/screens/edit_train/widgets/custom_drop_down.dart';
import 'package:railops/screens/edit_train/widgets/days_of_week_selector.dart';
import 'package:railops/screens/edit_train/widgets/frequency_checkboxes.dart';
import 'package:railops/screens/edit_train/widgets/stappages_sequence_update.dart';
import 'package:railops/screens/edit_train/widgets/stoppage_sequence.dart';
import 'package:railops/services/add_trains_services/add_train_service.dart';
import 'package:railops/services/assign_ehk_ca_services/return_gap_services.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/services/train_services/train_service_signup.dart';
import 'package:railops/types/assign_ehk_ca_types/return_gap_types.dart';
import 'package:railops/types/train_types/zone_division_type.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';
import 'package:railops/widgets/error_modal.dart';
import 'package:railops/widgets/loader.dart';
import 'package:railops/widgets/success_modal.dart';
import 'package:railops/screens/edit_train/widgets/stoppages_sequence_input.dart';

class AddTrainDetailsScreen extends StatefulWidget {
  const AddTrainDetailsScreen({Key? key}) : super(key: key);

  @override
  State<AddTrainDetailsScreen> createState() => _AddTrainDetailsScreenState();
}

class _AddTrainDetailsScreenState extends State<AddTrainDetailsScreen> {
  final _mainFormKey = GlobalKey<FormState>();
  final _returnGapFormKey = GlobalKey<FormState>();
  bool _mounted = true;
  Timer? _debounceTimer;
  Key _stoppageSequenceKey = UniqueKey();
  // Non-nullable controllers with initialization in constructor
  late final TextEditingController _trainNumberController;
  late final TextEditingController _trainNameController;
  late final TextEditingController _trainTypeController;
  late final TextEditingController _relatedTrainController;
  late final TextEditingController _startTimeController;
  late final TextEditingController _endTimeController;
  late final TextEditingController _chartingTimeController;
  late final TextEditingController _stoppageController;
  late final TextEditingController _coachesController;
  late final TextEditingController _fromStationController;
  late final TextEditingController _toStationController;

  String? zone;
  String? division;
  String? Depot;
  List<int> frequency = [];
  String? chartingDay;
  List<String> stoppages = [];
  List<String> coaches = [];
  String? updown;
  String token = '';
  bool isLoading = false;

  int? return_gap;
  String? in_out;
  bool _zonesLoaded = false;

  final List<String> daysOfWeek = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday"
  ];

  final List<String> updownOptions = ["up", "down"];
  final List<String> chartingDayOptions = ["Today", "Yesterday"];
  List<String> stoppagesForAttendance = [];
  List<String> activeStoppagesForAttendance = [];
  List<ZoneDivision> zones = [];
  List<String> divisions = [];
  List<String> Depots = [];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadUserToken();
  }

  void _loadUserToken() {
    Future.microtask(() {
      if (!_mounted) return;
      try {
        final userModel = Provider.of<UserModel>(context, listen: false);
        setState(() {
          token = userModel.token;
        });
      } catch (e) {
        _showError('Error initializing screen: $e');
      }
    });
  }

  void _initializeControllers() {
    _trainNumberController = TextEditingController();
    _trainNameController = TextEditingController();
    _trainTypeController = TextEditingController();
    _relatedTrainController = TextEditingController();
    _startTimeController = TextEditingController();
    _endTimeController = TextEditingController();
    _chartingTimeController = TextEditingController();
    _stoppageController = TextEditingController();
    _coachesController = TextEditingController();
    _fromStationController = TextEditingController();
    _toStationController = TextEditingController();
  }

  @override
  void dispose() {
    _mounted = false;
    _debounceTimer?.cancel();
    _trainNumberController.dispose();
    _trainNameController.dispose();
    _trainTypeController.dispose();
    _relatedTrainController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    _chartingTimeController.dispose();
    _stoppageController.dispose();
    _coachesController.dispose();
    _fromStationController.dispose();
    _toStationController.dispose();
    super.dispose();
  }

  @override
  void setState(fn) {
    if (_mounted) {
      super.setState(fn);
    }
  }

  Future<void> _fetchZones() async {
    try {
      if (!_mounted) return;
      final fetchedZones = await TrainServiceSignup.getZones();
      if (!_mounted) return;
      setState(() {
        zones = fetchedZones;
        isLoading = false;
      });
    } catch (e) {
      if (!_mounted) return;
      setState(() => isLoading = false);
      _showError('Failed to load zones: $e');
    }
  }

  Future<void> _fetchDivisions(String selectedZone) async {
    try {
      if (!_mounted) return;
      setState(() {
        divisions = [];
        division = null;
      });
      final fetchedDivisions =
          await TrainServiceSignup.getDivisions(selectedZone);
      if (!_mounted) return;
      setState(() {
        divisions = fetchedDivisions.map((div) => div.code).toList();
        isLoading = false;
      });
    } catch (e) {
      if (!_mounted) return;
      setState(() => isLoading = false);
      _showError('Failed to load divisions: $e');
    }
  }

  Future<void> _fetchDepots(String selectedDivision) async {
    try {
      if (!_mounted) return;
      setState(() {
        Depots = [];
        Depot = null;
      });
      final fetchedDepots = await TrainServiceSignup.getDepot(selectedDivision);
      if (!_mounted) return;
      setState(() {
        Depots = fetchedDepots;
        isLoading = false;
      });
    } catch (e) {
      if (!_mounted) return;
      setState(() => isLoading = false);
      _showError('Failed to load Depots: $e');
    }
  }

  // void _updateStoppages(String stoppageName, String action) async {
  //   try {
  //     loader(context, "Adding Data...");
  //     final trainDetails =
  //         await EditTrainServices.updateStoppagesForAttendanceByTrainNo(
  //       _trainNumberController.text,
  //       action,
  //       stoppageName,
  //       token,
  //     );

  //     if (trainDetails != null) {
  //       if (mounted) {
  //         setState(() {
  //           stoppagesForAttendance = trainDetails.allStations
  //                   .where((stoppage) =>
  //                       !trainDetails.attendanceStations!.contains(stoppage))
  //                   .toList() ??
  //               [];
  //           activeStoppagesForAttendance =
  //               trainDetails.attendanceStations ?? [];
  //         });
  //       }
  //       Navigator.of(context).pop();
  //       showSuccessModal(context, trainDetails.message, "Success", () {});
  //     }
  //     Navigator.of(context).pop();
  //   } catch (e) {
  //     Navigator.of(context).pop();
  //     showSuccessModal(context, '$e', "Success", () {});
  //   }
  // }

  void _addStoppages(List<String> updatedStoppages) {
    setState(() {
      stoppages = updatedStoppages;
    });
    print(stoppages);
  }

  void _handleFrequencyChange(List<int> selectedDays) {
    if (!_mounted) return;

    // Only update if there's an actual change
    if (!(frequency.length == selectedDays.length &&
        frequency.every((e) => selectedDays.contains(e)))) {
      setState(() {
        frequency = selectedDays;
      });
    }
  }

  Future<void> _submitForm() async {
    if (!_mainFormKey.currentState!.validate()) return;
    _mainFormKey.currentState!.save();
    try {
      setState(() {
        isLoading = true;
      });

      // Show loader while submitting
      loader(context, "Submitting train details...");

      final result = await AddTrainServices.addTrainDetails(
        token: token,
        trainNo: int.tryParse(_trainNumberController.text),
        trainName: _trainNameController.text,
        trainType: _trainTypeController.text,
        relatedTrain: int.tryParse(_relatedTrainController.text),
        fromStation: _fromStationController.text,
        toStation: _toStationController.text,
        startTime: _startTimeController.text,
        endTime: _endTimeController.text,
        chartingTime: _chartingTimeController.text,
        chartingDay: chartingDay,
        frequency: frequency,
        stoppagesInSequence: stoppages,
        updown: updown,
        zone: zone,
        division: division,
        Depot: Depot,
        return_gap: return_gap,
        in_out: in_out,
        coaches: coaches,
      );

      if (Navigator.canPop(context)) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      showSuccessModal(context, result!.message, "Success", () {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => const EditTrainScreen(),
          ),
        );
      });
      
    } catch (e) {
      if (Navigator.canPop(context)) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      if (!_mounted) return;
      setState(() {
        isLoading = false;
      });
    }
  }

  void _resetForm() {
    _stoppageSequenceKey = UniqueKey();
    Future.delayed(const Duration(milliseconds: 100), () {
      if (!_mounted) return;
      setState(() {
        _trainNumberController.clear();
        _trainNameController.clear();
        _trainTypeController.clear();
        _relatedTrainController.clear();
        _startTimeController.clear();
        _endTimeController.clear();
        _chartingTimeController.clear();
        _stoppageController.clear();
        zone = null;
        Depot = null;
        division = null;
        frequency = [];
        chartingDay = null;
        updown = null;
        stoppages = [];
        return_gap = null;
        in_out = null;
        coaches = [];
      });
    });
  }

  void _showError(String message) {
    if (!_mounted) return;
    showErrorModal(
      context,
      message,
      "Error",
      () {},
    );
  }

  Future<void> _selectTime(
    BuildContext context,
    TextEditingController controller,
  ) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (picked != null && _mounted) {
      final now = DateTime.now();
      final dateTime =
          DateTime(now.year, now.month, now.day, picked.hour, picked.minute);
      final formattedTime = DateFormat('HH:mm').format(dateTime);
      controller.text = formattedTime;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: "Add Train Details"),
      drawer: const CustomDrawer(),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                  const Padding(
                    padding: EdgeInsets.only(top: 5),
                    child: Text("Add Configuration",
                        style: TextStyle(fontSize: 18,fontWeight: FontWeight.w600),
                        textAlign: TextAlign.center,),
                  ),
                    // Train Number Card
                    const SizedBox(height: 10.0),
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Builder(
                          builder: (context) => TextFormField(
                            controller: _trainNumberController,
                            keyboardType: TextInputType.number,
                            inputFormatters: <TextInputFormatter>[
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(5),
                          ],
                            decoration: const InputDecoration(
                              labelText: 'Train Number',
                              border: OutlineInputBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(8.0)),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter train number';
                              }
                              if (value.length < 4) {
                                return 'Train number must be at least 4 digits';
                              }
                              return null;
                            },
                            onChanged: (value) {
                              _debounceTimer?.cancel();
                              if (value.length >= 4) {
                                _debounceTimer = Timer(
                                    const Duration(milliseconds: 500), () {
                                  if (_mounted && !_zonesLoaded) {
                                    _fetchZones();
                                    _zonesLoaded = true;
                                  }
                                });
                              } else {
                                setState(() {
                                  zones = [];
                                  division = null;
                                  Depot = null;
                                  _zonesLoaded = false;
                                });
                              }
                            },
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 10.0),

                    // Return Gap Card
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Form(
                          key: _returnGapFormKey,
                          child: Builder(
                            builder: (context) => Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: DropdownButtonFormField<String>(
                                        value: return_gap?.toString(),
                                        items: List.generate(8, (index) {
                                          final gap = (index).toString();
                                          return DropdownMenuItem(
                                            value: gap,
                                            child: Text(gap),
                                          );
                                        }),
                                        decoration: const InputDecoration(
                                          labelText: 'Return Gap (Days)',
                                          border: OutlineInputBorder(),
                                          filled: true,
                                          fillColor: Colors.white,
                                        ),
                                        onChanged: (value) {
                                          setState(() {
                                            return_gap =
                                                int.tryParse(value ?? "0");
                                          });
                                        },
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Please select a return gap in days';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                    const SizedBox(width: 8.0),
                                    Expanded(
                                      child: CustomDropdownField(
                                        value: in_out,
                                        items: const ['in', 'out'],
                                        onChanged: (value) =>
                                            setState(() => in_out = value),
                                        labelText: 'In/Out',
                                        right: 4.0,
                                        left: 0.0,
                                        top: 8.0,
                                        bottom: 8.0,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 10.0),
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Coaches Sequence',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 10.0),
                            CoachesInputField(
                              controller: _coachesController,
                              onCoachesUpdated: (updatedCoaches) {
                                setState(() {
                                  coaches = updatedCoaches;
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 10.0),
                    // Stoppages Card with ExpansionPanel
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Stoppages in Sequence',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            StoppagesSequenceInput(
                              stoppages: stoppages,
                              stoppageController: _stoppageController,
                              onStoppagesUpdated: _addStoppages,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 10.0),
                    // All Remaining Details Card
                    Card(
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Form(
                          key: _mainFormKey,
                          child: Builder(
                            builder: (context) => Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Train Name
                                TextFormField(
                                  controller: _trainNameController,
                                  decoration: const InputDecoration(
                                    labelText: 'Train Name',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(8.0)),
                                    ),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter train name';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 15.0),

                                // Related Train
                                TextFormField(
                                  controller: _relatedTrainController,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    labelText: 'Related Train Number',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(8.0)),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 15.0),

                                // Zone, Division, Depot
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Flexible(
                                      child: DropdownButtonFormField<String?>(
                                        value: zone,
                                        decoration: const InputDecoration(
                                          labelText: 'Zone',
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(4.0)),
                                          ),
                                        ),
                                        items: zones.map((zoneItem) {
                                          return DropdownMenuItem<String?>(
                                            value: zoneItem.code,
                                            child: Text(zoneItem.code),
                                          );
                                        }).toList(),
                                        onChanged: (newValue) {
                                          setState(() {
                                            zone = newValue;
                                            division = null;
                                            Depot = null;
                                          });
                                          if (newValue != null) {
                                            _fetchDivisions(newValue);
                                          }
                                        },
                                      ),
                                    ),
                                    const SizedBox(width: 5.0),
                                    Flexible(
                                      child: DropdownButtonFormField<String?>(
                                        value: division,
                                        decoration: const InputDecoration(
                                          labelText: 'Division',
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(4.0)),
                                          ),
                                        ),
                                        items: divisions.map((divisionItem) {
                                          return DropdownMenuItem<String?>(
                                            value: divisionItem,
                                            child: Text(divisionItem),
                                          );
                                        }).toList(),
                                        onChanged: (newValue) {
                                          setState(() {
                                            division = newValue;
                                            Depot = null;
                                          });
                                          if (newValue != null) {
                                            _fetchDepots(newValue);
                                          }
                                        },
                                      ),
                                    ),
                                    const SizedBox(width: 5.0),
                                    Flexible(
                                      child: DropdownButtonFormField<String?>(
                                        value: Depot,
                                        decoration: const InputDecoration(
                                          labelText: 'Depot',
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(4.0)),
                                          ),
                                        ),
                                        items: Depots.map((DepotItem) {
                                          return DropdownMenuItem<String?>(
                                            value: DepotItem,
                                            child: Text(DepotItem),
                                          );
                                        }).toList(),
                                        onChanged: (newValue) {
                                          setState(() => Depot = newValue);
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 5.0),
                                DaysOfWeekSelector(
                                  selectedDays: frequency,
                                  onSelectionChanged: _handleFrequencyChange,
                                ),

                                const SizedBox(height: 15.0),
                                // Charting Day
                                DropdownButtonFormField<String?>(
                                  value: chartingDay,
                                  decoration: const InputDecoration(
                                    labelText: 'Charting Day',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(8.0)),
                                    ),
                                  ),
                                  items: [
                                    const DropdownMenuItem<String?>(
                                      value: null,
                                      child: Text("Select Charting Day"),
                                    ),
                                    ...chartingDayOptions.map((String value) {
                                      return DropdownMenuItem<String?>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    }).toList(),
                                  ],
                                  onChanged: (newValue) {
                                    if (!_mounted) return;
                                    setState(() {
                                      chartingDay = newValue;
                                    });
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please select charting day';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 15.0),
                                // From/To Station
                                Row(
                                  children: [
                                    Expanded(
                                      child: Builder(
                                        builder: (context) => TextFormField(
                                          controller: _fromStationController,
                                          decoration: const InputDecoration(
                                            labelText: 'From Station',
                                            border: OutlineInputBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(8.0)),
                                            ),
                                          ),
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'Please enter from station';
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8.0),
                                    Expanded(
                                      child: Builder(
                                        builder: (context) => TextFormField(
                                          controller: _toStationController,
                                          decoration: const InputDecoration(
                                            labelText: 'To Station',
                                            border: OutlineInputBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(8.0)),
                                            ),
                                          ),
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'Please enter to station';
                                            }
                                            return null;
                                          },
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 15.0),
                                DropdownButtonFormField<String>(
                                  value: updown,
                                  decoration: const InputDecoration(
                                    labelText: 'Up/Down',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(Radius.circular(8.0)),
                                    ),
                                  ),
                                  items: updownOptions.map((String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    if (!_mounted) return;
                                    setState(() {
                                      updown = newValue ?? 'up'; // Default to 'up' if null
                                    });
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please select direction';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 15.0),

                                // Time Fields
                                Row(
                                  children: [
                                    Expanded(
                                      child: Builder(
                                        builder: (context) => TextFormField(
                                          controller: _startTimeController,
                                          decoration: const InputDecoration(
                                            labelText: 'Start Time',
                                            border: OutlineInputBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(8.0)),
                                            ),
                                          ),
                                          onTap: () => _selectTime(
                                            context,
                                            _startTimeController,
                                          ),
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'Please enter start time';
                                            }
                                            return null;
                                          },
                                          readOnly: true,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8.0),
                                    Expanded(
                                      child: Builder(
                                        builder: (context) => TextFormField(
                                          controller: _endTimeController,
                                          decoration: const InputDecoration(
                                            labelText: 'End Time',
                                            border: OutlineInputBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(8.0)),
                                            ),
                                          ),
                                          onTap: () => _selectTime(
                                            context,
                                            _endTimeController,
                                          ),
                                          validator: (value) {
                                            if (value == null ||
                                                value.isEmpty) {
                                              return 'Please enter end time';
                                            }
                                            return null;
                                          },
                                          readOnly: true,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 15.0),

                                // Charting Time
                                Builder(
                                  builder: (context) => TextFormField(
                                    controller: _chartingTimeController,
                                    decoration: const InputDecoration(
                                      labelText: 'Charting Time',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(8.0)),
                                      ),
                                    ),
                                    onTap: () => _selectTime(
                                      context,
                                      _chartingTimeController,
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Please enter charting time';
                                      }
                                      return null;
                                    },
                                    readOnly: true,
                                  ),
                                ),
                                const SizedBox(height: 15.0),

                                // Train Type
                                TextFormField(
                                  controller: _trainTypeController,
                                  decoration: const InputDecoration(
                                    labelText: 'Train Type',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(8.0)),
                                    ),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter train type';
                                    }
                                    return null;
                                  },
                                ),

                                const SizedBox(height: 10),
                                // Submit Button
                                Center(
                                  child: ElevatedButton(
                                    onPressed: _submitForm,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 40,
                                        vertical: 15,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: const Text(
                                      'Submit',
                                      style: TextStyle(fontSize: 16),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
