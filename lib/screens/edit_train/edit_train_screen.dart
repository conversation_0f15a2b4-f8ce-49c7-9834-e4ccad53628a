import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/assign_ehk_ca_screen/widgets/return_gap.dart';
import 'package:railops/screens/edit_train/add_train_screen.dart';
import 'package:railops/screens/edit_train/widgets/coach_in_sequence.dart';
import 'package:railops/screens/edit_train/widgets/stappages_sequence_update.dart';
import 'package:railops/screens/profile_screen/add_train_screen.dart';
import 'package:railops/services/assign_ehk_ca_services/return_gap_services.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:railops/services/train_services/train_service_signup.dart';
import 'package:railops/types/assign_ehk_ca_types/return_gap_types.dart';
import 'package:railops/types/train_types/zone_division_type.dart';
import 'package:railops/widgets/index.dart';
import 'form/edit_train_form.dart';

class EditTrainScreen extends StatefulWidget {
  const EditTrainScreen({Key? key}) : super(key: key);

  @override
  _EditTrainScreenState createState() => _EditTrainScreenState();
}

class _EditTrainScreenState extends State<EditTrainScreen> {
  final _formKey = GlobalKey<FormState>();

  late final TextEditingController _trainNameController;
  late final TextEditingController _trainTypeController;
  late final TextEditingController _relatedTrainController;
  late final TextEditingController _startTimeController;
  late final TextEditingController _endTimeController;
  late final TextEditingController _chartingTimeController;
  late final TextEditingController _stoppageController;
  late final TextEditingController fromStationController;
  late final TextEditingController toStationController;

  String? relatedTrain;
  String? zone;
  String? depot;
  String? division;
  List<int> frequency = [];

  String? chartingDay;
  List<String> stoppages = [];
  List<String> trainNumbers = [];
  String? selectedTrainNumber;
  List<String> stoppagesForAttendnace = [];
  List<String> activeStoppagesForAttendnace = [];
  bool trainLoading = false;

  String? fromStation;
  String? toStation;
  String? updown;
  Map<String, String> extraInfo = {};

  String? token;
  bool _isDisposed = false;
  bool _isInitialized = false;

  final List<String> daysOfWeek = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday"
  ];

  String? train;
  bool isFormEnabled = false;
  List<ZoneDivision> zones = [];
  List<String> divisions = [];
  List<String> depots = [];
  bool zonesLoading = false;
  bool divisionsLoading = false;
  bool depotsLoading = false;
  bool _zonesFetched = false;
  ReturnGapData? returnGapData;
  bool _isDataLoading = false;

  bool _isCurrentlyLoading = false;

  @override
  void initState() {
    super.initState();

    _trainNameController = TextEditingController(text: '');
    _trainTypeController = TextEditingController(text: '');
    _relatedTrainController = TextEditingController(text: '');
    _startTimeController = TextEditingController(text: '');
    _endTimeController = TextEditingController(text: '');
    _chartingTimeController = TextEditingController(text: '');
    _stoppageController = TextEditingController(text: '');
    fromStationController = TextEditingController(text: '');
    toStationController = TextEditingController(text: '');
    train = Provider.of<UserModel>(context, listen: false).trainNo;
    _isInitialized = true;
    _isDataLoading = false;

    Future.microtask(() {
      if (mounted && !_isDisposed) {
        final userModel = Provider.of<UserModel>(context, listen: false);
        token = userModel.token;
        _fetchTrainNumbers();
        _fetchZones();
      }
    });
  }

  @override
  void dispose() {
    _isDisposed = true;

    if (_isInitialized) {
      _trainNameController.dispose();
      _trainTypeController.dispose();
      _relatedTrainController.dispose();
      _startTimeController.dispose();
      _endTimeController.dispose();
      _chartingTimeController.dispose();
      _stoppageController.dispose();
      fromStationController.dispose();
      toStationController.dispose();
    }

    super.dispose();
  }

  void setStateIfMounted(VoidCallback fn) {
    if (mounted && !_isDisposed) {
      setState(fn);
    }
  }

  Future<void> _fetchTrainNumbers() async {
    if (_isDisposed || _isCurrentlyLoading) return;

    try {
      setStateIfMounted(() {
        trainLoading = true;
        _isCurrentlyLoading = true;
      });

      final tempTrainNumbers = await TrainService.getTrainNumbers();

      if (_isDisposed) return;

      setStateIfMounted(() {
        trainNumbers = tempTrainNumbers;
        trainLoading = false;
        _isCurrentlyLoading = false;
      });

      if (train != null && trainNumbers.contains(train)) {
        _onTrainNumberChanged(train);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching train numbers: $e');
      }
      if (!_isDisposed) {
        setStateIfMounted(() {
          trainLoading = false;
          _isCurrentlyLoading = false;
        });
      }
    }
  }

  void _showError(String message) {
    if (_isDisposed || !mounted || !context.mounted) return;

    showErrorModal(
      context,
      message,
      "Error",
      () {},
    );
  }

  Future<void> _fetchZones() async {
    if (_isDisposed || _zonesFetched || _isCurrentlyLoading) return;

    try {
      setStateIfMounted(() {
        zonesLoading = true;
        _isCurrentlyLoading = true;
      });

      final fetchedZones = await TrainServiceSignup.getZones();

      if (_isDisposed) return;

      setStateIfMounted(() {
        zones = fetchedZones;
        zonesLoading = false;
        _zonesFetched = true;
        _isCurrentlyLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;

      setStateIfMounted(() {
        zonesLoading = false;
        _isCurrentlyLoading = false;
      });
      _showError('Failed to load zones: $e');
    }
  }

  Future<void> _fetchDivisions(String selectedZone) async {
    if (_isDisposed || _isCurrentlyLoading) return;

    try {
      setStateIfMounted(() {
        divisionsLoading = true;
        divisions = [];
        _isCurrentlyLoading = true;
      });

      final fetchedDivisions =
          await TrainServiceSignup.getDivisions(selectedZone);

      if (_isDisposed) return;

      setStateIfMounted(() {
        divisions = fetchedDivisions.map((div) => div.code).toList();
        divisionsLoading = false;
        _isCurrentlyLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;

      setStateIfMounted(() {
        divisionsLoading = false;
        _isCurrentlyLoading = false;
      });
      _showError('Failed to load divisions: $e');
    }
  }

  Future<void> _fetchDepots(String selectedDivision) async {
    if (_isDisposed || _isCurrentlyLoading) return;

    try {
      setStateIfMounted(() {
        depotsLoading = true;
        depots = [];
        _isCurrentlyLoading = true;
      });

      final fetchedDepots = await TrainServiceSignup.getDepot(selectedDivision);

      if (_isDisposed) return;

      setStateIfMounted(() {
        depots = fetchedDepots;
        depotsLoading = false;
        _isCurrentlyLoading = false;
      });
    } catch (e) {
      if (_isDisposed) return;

      setStateIfMounted(() {
        depotsLoading = false;
        _isCurrentlyLoading = false;
      });
      _showError('Failed to load depots: $e');
    }
  }

  Future<ReturnGapData?> fetchReturnGap(String trainId) async {
    if (_isDisposed || _isCurrentlyLoading) return null;

    try {
      setStateIfMounted(() {
        _isCurrentlyLoading = true;
      });

      final tempReturnGapData =
          await ReturnGapService.fetchReturnGapByTrain(trainId);

      if (_isDisposed) return null;

      setStateIfMounted(() {
        returnGapData = tempReturnGapData;
        _isCurrentlyLoading = false;
      });

      if (returnGapData != null) {
        if (kDebugMode) {
          print('Return Gap Data fetched successfully: $tempReturnGapData');
        }
      } else {
        if (kDebugMode) {
          print('No Return Gap Data found for Train ID: $trainId');
        }
      }
      return returnGapData;
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching Return Gap Data: $e');
      }

      if (_isDisposed) return null;

      setStateIfMounted(() {
        returnGapData = null;
        _isCurrentlyLoading = false;
      });
      return null;
    }
  }

  void updateTimeControllers(String startTime, String endTime) {
    if (!_isDisposed && mounted) {
      setState(() {
        _startTimeController.text = startTime;
        _endTimeController.text = endTime;
      });
    }
  }

  Future<void> _fetchTrainDetails(String trainNo) async {
    if (_isDisposed || _isCurrentlyLoading) return;

    try {
      setStateIfMounted(() {
        _isDataLoading = true;
        _isCurrentlyLoading = true;
      });

      final trainDetails =
          await EditTrainServices.fetchTrainDetailsByTrainNo(trainNo);

      if (_isDisposed) return;

      if (trainDetails != null) {
        updateTimeControllers(
          trainDetails.startTime ?? '',
          trainDetails.endTime ?? '',
        );
        setStateIfMounted(() {
          _trainNameController.text = trainDetails.trainName ?? 'NA';
          _trainTypeController.text = trainDetails.trainType ?? 'NA';

          if (trainDetails.relatedTrain != null) {
            relatedTrain = trainDetails.relatedTrain.toString().padLeft(5, '0');
            _relatedTrainController.text = relatedTrain ?? '';
          } else {
            _relatedTrainController.text = '';
          }

          _startTimeController.text = trainDetails.startTime ?? 'NA';
          _endTimeController.text = trainDetails.endTime ?? 'NA';
          _chartingTimeController.text = trainDetails.chartingTime ?? 'NA';

          fromStationController.text = trainDetails.fromStation ?? '';
          toStationController.text = trainDetails.toStation ?? '';

          stoppages = trainDetails.stoppagesInSequence ?? [];
          frequency = trainDetails.frequency ?? [];
          chartingDay = trainDetails.chartingDay;
          fromStation = trainDetails.fromStation;
          toStation = trainDetails.toStation;
          updown = trainDetails.updown;

          extraInfo.clear();
          if (trainDetails.extraInfo != null) {
            extraInfo.addAll(trainDetails.extraInfo!);
          }

          _isCurrentlyLoading = false;

          if (extraInfo.containsKey('zone_code') &&
              extraInfo['zone_code'] != null) {
            zone = extraInfo['zone_code'];
          }

          if (trainDetails.division != null &&
              trainDetails.division!.isNotEmpty) {
            division = trainDetails.division;
          }

          if (trainDetails.depot != null) {
            depot = trainDetails.depot;
          }
        });

        if (!_isDisposed &&
            extraInfo.containsKey('zone_code') &&
            extraInfo['zone_code'] != null) {
          final zoneCode = extraInfo['zone_code'];
          if (zoneCode != null && zoneCode.isNotEmpty) {
            await _fetchDivisions(zoneCode);

            if (_isDisposed) return;

            if (trainDetails.division != null &&
                trainDetails.division!.isNotEmpty) {
              await _fetchDepots(trainDetails.division!);
            }
          }
        }

        if (!_isDisposed) {
          setStateIfMounted(() {
            _isDataLoading = false;
          });
        }
      } else {
        if (!_isDisposed) {
          setStateIfMounted(() {
            _isDataLoading = false;
            _isCurrentlyLoading = false;
          });
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching train details: $e');
      }

      if (_isDisposed) return;

      setStateIfMounted(() {
        _isDataLoading = false;
        _isCurrentlyLoading = false;
      });

      if (mounted && context.mounted) {
        showErrorModal(
            context, 'Failed to load train details: $e', "Error", () {});
      }
    }
  }

  Future<void> _fetchStoppagesDetails(String trainNo) async {
    if (_isDisposed || token == null || _isCurrentlyLoading) return;

    try {
      setStateIfMounted(() {
        _isCurrentlyLoading = true;
      });

      final trainDetails =
          await EditTrainServices.fetchStoppagesForAttendanceByTrainNo(
              trainNo, token!);

      if (_isDisposed) return;

      if (trainDetails != null) {
        setStateIfMounted(() {
          final allStations = trainDetails.allStations;
          final attendanceStations = trainDetails.attendanceStations;

          stoppagesForAttendnace = allStations
              .where((stoppage) => !attendanceStations.contains(stoppage))
              .toList();
          activeStoppagesForAttendnace = List<String>.from(attendanceStations);
          _isCurrentlyLoading = false;
        });
      } else {
        if (!_isDisposed) {
          setStateIfMounted(() {
            _isCurrentlyLoading = false;
          });
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching stoppages details: $e');
      }
      if (!_isDisposed) {
        setStateIfMounted(() {
          _isCurrentlyLoading = false;
        });
      }
    }
  }

  Future<void> addOrUpdateReturnGapWithParams({
    required int days,
    required String inOut,
  }) async {
    if (_isDisposed ||
        train == null ||
        token == null ||
        !mounted ||
        !context.mounted ||
        _isCurrentlyLoading) {
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return const Dialog(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text("Submitting..."),
              ],
            ),
          ),
        );
      },
    );

    setStateIfMounted(() {
      _isCurrentlyLoading = true;
    });

    final request = ReturnGapRequest(
      days: days,
      trainNo: train!,
      token: token!,
      inOut: inOut,
    );

    try {
      await ReturnGapService.addOrUpdateReturnGap(request);

      if (_isDisposed || !mounted || !context.mounted) return;

      Navigator.of(context).pop();

      setStateIfMounted(() {
        _isCurrentlyLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Return gap updated successfully')),
      );
    } catch (error) {
      if (_isDisposed || !mounted || !context.mounted) return;

      Navigator.of(context).pop();

      setStateIfMounted(() {
        _isCurrentlyLoading = false;
      });

      showErrorModal(context, '$error', "Error", () {});
    }
  }

  void handleReturnGapSubmission(int? gap, String? inOut) {
    if (gap != null && inOut != null) {
      addOrUpdateReturnGapWithParams(days: gap, inOut: inOut);
    }
  }

  void _updateStoppages(String stoppageName, String action) async {
    if (_isDisposed ||
        selectedTrainNumber == null ||
        token == null ||
        !mounted ||
        !context.mounted ||
        _isCurrentlyLoading) {
      return;
    }

    loader(context, "Updating Data...");

    setStateIfMounted(() {
      _isCurrentlyLoading = true;
    });

    try {
      final trainDetails =
          await EditTrainServices.updateStoppagesForAttendanceByTrainNo(
        selectedTrainNumber!,
        action,
        stoppageName,
        token!,
      );

      if (_isDisposed || !mounted || !context.mounted) return;

      Navigator.of(context).pop();

      if (trainDetails != null) {
        setStateIfMounted(() {
          final allStations = trainDetails.allStations;
          final attendanceStations = trainDetails.attendanceStations;

          stoppagesForAttendnace = allStations
              .where((stoppage) => !attendanceStations.contains(stoppage))
              .toList();
          activeStoppagesForAttendnace = List<String>.from(attendanceStations);
          _isCurrentlyLoading = false;
        });

        if (mounted && context.mounted) {
          showSuccessModal(context, trainDetails.message, "Success", () {});
        }
      } else {
        setStateIfMounted(() {
          _isCurrentlyLoading = false;
        });
      }
    } catch (e) {
      if (_isDisposed || !mounted || !context.mounted) return;

      Navigator.of(context).pop();

      setStateIfMounted(() {
        _isCurrentlyLoading = false;
      });

      showErrorModal(context, '$e', "Error", () {});
    }
  }

  Future<void> _reloadPage() async {
    if (_isDisposed || !mounted || _isCurrentlyLoading) return;

    try {
      if (selectedTrainNumber != null) {
        setStateIfMounted(() {
          _isDataLoading = true;
          _isCurrentlyLoading = true;
        });

        await fetchReturnGap(selectedTrainNumber!);

        if (_isDisposed) return;

        await _fetchTrainDetails(selectedTrainNumber!);

        if (_isDisposed) return;

        await _fetchStoppagesDetails(selectedTrainNumber!);

        if (_isDisposed) return;

        if (!_zonesFetched) {
          await _fetchZones();
        }

        if (_isDisposed) return;

        setStateIfMounted(() {
          _isDataLoading = false;
          _isCurrentlyLoading = false;
        });

        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Data refreshed successfully')),
          );
        }
      }
    } catch (e) {
      if (_isDisposed) return;

      setStateIfMounted(() {
        _isDataLoading = false;
        _isCurrentlyLoading = false;
      });

      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Data not refreshed: $e')),
        );
      }
    }
  }

  void _submitForm(
      String? relatedTrain,
      String? zone,
      String? depot,
      String? division,
      List<int>? frequency,
      String? chartingDay,
      List<String> stoppages) {
    if (_isDisposed ||
        train == null ||
        token == null ||
        !mounted ||
        !context.mounted ||
        _isCurrentlyLoading) {
      return;
    }

    Map<String, dynamic> extraInfoMap = {};
    if (zone != null) {
      extraInfoMap['zone_code'] = zone;
    }
    if (division != null) {
      extraInfoMap['division_code'] = division;
    }
    if (depot != null) {
      extraInfoMap['depot_code'] = depot;
    }

    Map<String, dynamic> request = {
      'token': token,
      'train_name': _trainNameController.text.isNotEmpty
          ? _trainNameController.text
          : null,
      'train_type': _trainTypeController.text.isNotEmpty
          ? _trainTypeController.text
          : null,
      'start_time': _startTimeController.text.isNotEmpty
          ? _startTimeController.text
          : null,
      'end_time':
          _endTimeController.text.isNotEmpty ? _endTimeController.text : null,
      'charting_time': _chartingTimeController.text.isNotEmpty
          ? _chartingTimeController.text
          : null,
      'related_train': relatedTrain != null ? int.tryParse(relatedTrain) : null,
      'division': division,
      'Depot': depot,
      'frequency': frequency,
      'charting_day': chartingDay,
      'stopages_in_sequence': stoppages,
      'train_no': train != null ? int.tryParse(train!) : null,
      'up_down': updown,
      'from_station': fromStation,
      'to_station': toStation,
      'extra_info': extraInfoMap.isNotEmpty ? extraInfoMap : null,
    };

    request.removeWhere((key, value) => value == null);
    request.removeWhere((key, value) => value == 'NA');

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return const Dialog(
          child: Padding(
            padding: EdgeInsets.all(16.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text("Submitting..."),
              ],
            ),
          ),
        );
      },
    );

    setStateIfMounted(() {
      _isCurrentlyLoading = true;
    });

    EditTrainServices.updateTrainDetails(train!, request).then((result) {
      if (_isDisposed || !mounted || !context.mounted) return;

      setStateIfMounted(() {
        _isCurrentlyLoading = false;
      });

      Navigator.of(context).pop();
      showSuccessModal(
          context, 'Train details updated successfully', "Success", () {});
    }).catchError((error) {
      if (_isDisposed || !mounted || !context.mounted) return;

      setStateIfMounted(() {
        _isCurrentlyLoading = false;
      });

      Navigator.of(context).pop();
      showErrorModal(context, '$error', "Error", () {});
    });
  }

  Widget buttonGroup() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              if (!_isDisposed && mounted && context.mounted) {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddTrainDetailsScreen(),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                side: BorderSide(color: Colors.black87, width: 0.5),
              ),
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              textStyle:
                  const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            child: const Text(
              'Add',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ),
        const SizedBox(width: 5.0),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              if (!_isDisposed && mounted && context.mounted) {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const EditTrainScreen(),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
                side: BorderSide(color: Colors.black87, width: 0.5),
              ),
              padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              textStyle:
                  const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            child: const Text(
              'Edit',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ),
      ],
    );
  }

  void _onTrainNumberChanged(String? value) {
    if (value != null && !_isCurrentlyLoading) {
      setStateIfMounted(() {
        train = value;
        selectedTrainNumber = value;
        isFormEnabled = true;
        _isDataLoading = true;
      });

      fetchReturnGap(value).then((_) {
        if (!_isDisposed) {
          return _fetchTrainDetails(value);
        }
      }).then((_) {
        if (!_isDisposed) {
          return _fetchStoppagesDetails(value);
        }
      }).then((_) {
        if (!_isDisposed) {
          setStateIfMounted(() {
            _isDataLoading = false;
          });
        }
      }).catchError((e) {
        if (!_isDisposed) {
          setStateIfMounted(() {
            _isDataLoading = false;
          });
          _showError('Error loading train data: $e');
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      appBar: const CustomAppBar(title: "Edit Train Details"),
      drawer: const CustomDrawer(),
      
      body: RefreshIndicator(
        
        onRefresh: _reloadPage,
        
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Container(
            constraints: BoxConstraints(
              minHeight: screenHeight,
            ),
            child: IntrinsicHeight(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const Padding(
                    padding: EdgeInsets.only(top: 5),
                    child: Text("Edit Configuration",
                        style: TextStyle(fontSize: 18,fontWeight: FontWeight.w600),
                         textAlign: TextAlign.center,
                      ),
                  ),
                  SizedBox(height: screenHeight * 0.02),
                  buttonGroup(),
                  SizedBox(height: screenHeight * 0.02),
                  if (trainNumbers.isNotEmpty)
                    DropdownSearch<String>(
                      popupProps: PopupProps.menu(
                        showSearchBox: true,
                        searchFieldProps: TextFieldProps(
                          keyboardType: TextInputType.number,
                          inputFormatters: <TextInputFormatter>[
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(5),
                          ],
                          decoration: InputDecoration(
                            hintText: "Search Train Number",
                            border: const OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: screenWidth * 0.05,
                            ),
                          ),
                        ),
                      ),
                      items: trainNumbers,
                      selectedItem: train,
                      dropdownDecoratorProps: DropDownDecoratorProps(
                        dropdownSearchDecoration: InputDecoration(
                          labelText: "Select Train Number",
                          border: const OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: screenWidth * 0.05,
                            vertical: screenHeight * 0.015,
                          ),
                        ),
                      ),
                      dropdownButtonProps: DropdownButtonProps(
                        icon: trainLoading
                            ? Padding(
                                padding: EdgeInsets.all(screenWidth * 0.02),
                                child: SizedBox(
                                  width: screenWidth * 0.53,
                                  height: screenHeight * 0.025,
                                  child: const CircularProgressIndicator(
                                      strokeWidth: 2),
                                ),
                              )
                            : const Icon(Icons.arrow_drop_down),
                      ),
                      onChanged: (value) {
                        _onTrainNumberChanged(value);
                      },
                    ),
                  SizedBox(height: screenHeight * 0.03),
                  if (_isDataLoading)
                    const Center(
                      child: CircularProgressIndicator(),
                    ),
                  if (selectedTrainNumber != null && !_isDataLoading)
                    ReturnGap(
                      onSubmit: handleReturnGapSubmission,
                      returnGapData: returnGapData,
                      isFormEnabled: isFormEnabled,
                    ),
                  if (selectedTrainNumber != null && !_isDataLoading) ...[
                    Expanded(
                      child: EditTrainForm(
                        key: _formKey,
                        trainNameController: _trainNameController,
                        trainTypeController: _trainTypeController,
                        relatedTrainController: _relatedTrainController,
                        startTimeController: _startTimeController,
                        endTimeController: _endTimeController,
                        chartingTimeController: _chartingTimeController,
                        stoppageController: _stoppageController,
                        fromStationController: fromStationController,
                        toStationController: toStationController,
                        relatedTrain: relatedTrain,
                        zone: zone,
                        depot: depot,
                        division: division,
                        frequency: frequency,
                        updown: updown,
                        extraInfo: extraInfo,
                        chartingDay: chartingDay,
                        stoppages: stoppages,
                        isFormEnabled: isFormEnabled,
                        onSubmit: () {
                          _submitForm(
                            relatedTrain,
                            zone,
                            depot,
                            division,
                            frequency,
                            chartingDay,
                            stoppages,
                          );
                        },
                        zones: zones,
                        divisions: divisions,
                        depots: depots,
                        zonesLoading: zonesLoading,
                        divisionsLoading: divisionsLoading,
                        depotsLoading: depotsLoading,
                        onZoneChanged: (newZone) {
                          if (newZone != zone) {
                            setStateIfMounted(() {
                              zone = newZone;
                              division = null;
                              depot = null;
                              divisions = [];
                              depots = [];
                            });
                            _fetchDivisions(newZone!);
                          }
                        },
                        onDivisionChanged: (newDivision) {
                          if (newDivision != division) {
                            setStateIfMounted(() {
                              division = newDivision;
                              depot = null;
                              depots = [];
                            });
                            _fetchDepots(newDivision!);
                          }
                        },
                        onDepotChanged: (newDepot) {
                          setStateIfMounted(() {
                            depot = newDepot;
                          });
                        },
                        onFrequencyChanged: (newFrequency) {
                          setStateIfMounted(() {
                            frequency = newFrequency;
                          });
                        },
                        onChartingDayChanged: (newChartingDay) {
                          setStateIfMounted(() {
                            chartingDay = newChartingDay;
                          });
                        },
                        onStoppagesChanged: (newStoppages) {
                          setStateIfMounted(() {
                            stoppages = newStoppages;
                          });
                        },
                        onFromStationChanged: (newFromStation) {
                          setStateIfMounted(() {
                            fromStation = newFromStation;
                            fromStationController.text = newFromStation ?? '';
                          });
                        },
                        onToStationChanged: (newToStation) {
                          setStateIfMounted(() {
                            toStation = newToStation;
                            toStationController.text = newToStation ?? '';
                          });
                        },
                        onTrainTypeChanged: (newTrainType) {
                          setStateIfMounted(() {
                            _trainTypeController.text = newTrainType ?? '';
                          });
                        },
                        daysOfWeek: daysOfWeek,
                        onRelatedTrainChanged: (value) {
                          setStateIfMounted(() {
                            relatedTrain = value;
                          });
                        },
                        onUpDownChanged: (value) {
                          setStateIfMounted(() {
                            updown = value;
                          });
                        },
                      ),
                    ),
                    SizedBox(height: screenHeight * 0.02),
                    if (selectedTrainNumber != null && !_isDataLoading)
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: EdgeInsets.all(screenWidth * 0.04),
                          child: CoachListWidget(
                            trainNo: selectedTrainNumber!,
                            token: token!,
                          ),
                        ),
                      ),
                    SizedBox(height: screenHeight * 0.02),
                    if (selectedTrainNumber != null && !_isDataLoading)
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: EdgeInsets.all(screenWidth * 0.04),
                          child: StappagesSequenceUpdate(
                            stoppages: stoppagesForAttendnace,
                            attendanceStoppages: activeStoppagesForAttendnace,
                            stoppageController: _stoppageController,
                            onStoppagesUpdated: (stoppageName, action) {
                              _updateStoppages(stoppageName, action);
                            },
                          ),
                        ),
                      ),
                    SizedBox(height: screenHeight * 0.02),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isFormEnabled
                            ? () {
                                _submitForm(
                                  relatedTrain,
                                  zone,
                                  depot,
                                  division,
                                  frequency,
                                  chartingDay,
                                  stoppages,
                                );
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            vertical: screenHeight * 0.02,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Submit',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
