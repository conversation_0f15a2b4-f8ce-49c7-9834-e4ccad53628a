import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/attendance/attendance_details.dart';
import 'package:railops/screens/attendance/image_upload.dart';
import 'package:railops/screens/profile_screen/widgets/change_email_modal.dart';
import 'package:railops/services/assign_ehk_ca_services/return_gap_services.dart';
import 'package:railops/services/attendance_services/attendance_sevices.dart';
import 'package:railops/services/attendance_services/location_service.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/services/location_services/location_services.dart';
import 'package:railops/services/profile_services/profile_services.dart';
import 'package:railops/services/profile_services/profile_train_services.dart';
import 'package:railops/types/assign_ehk_ca_types/return_gap_types.dart';
import 'package:railops/types/attendance_types/onboarding_response.dart';
import 'package:railops/types/edit_train_types/edit_train_types.dart';
import 'package:railops/types/train_types/train_charting_response.dart';
import 'package:railops/utils/permission_handler_service.dart';
import 'package:railops/widgets/index.dart';
import 'package:railops/services/train_services/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:permission_handler/permission_handler.dart';

class AttendanceScreen extends StatefulWidget {
  @override
  _AttendanceScreenState createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  String? selectedTrain;
  List<String> stations = [];
  String startTime = "NA";
  String endTime = "NA";
  String trainScheduledAt = "NA";
  int journeyDays = 2;
  List<String> refreshedTimes = [];
  String expectedChartingTime = "NA";
  String loadedAtRecent = "NA";
  String loadedAtEarliest = "NA";
  String ehkName = "Na";
  String depot = "Na";
  bool isRunningDay = false;
  String lastFetchedFormattedDate = "";
  bool lastStationLocationFetched = false;
  final TextEditingController _dateController = TextEditingController();
  DateTime selectedDate = DateTime.now();
  List<String> _trainNumbers = [];
  Map<String, String> stationNames = {};
  String? token;
  Map<String, int> stationAttendanceCount = {};
  OnboardingResponse onboardingResponse = OnboardingResponse(
      message: '',
      stations: [],
      lastLocationFetched: null,
      lastLocationFetchedFromUser: null,
      date: null,
      trainNumber: null,
      coachNumbers: [],
      details: {},
      detailsOffBoarding: {},
      detailsOffBoardingInroute: {},
      detailsInroute: {});
  List<String> onboardingStations = [];
  List<String> coachNumbers = [];
  TrainChartingResponse? chartData;
  List<String> attendanceStations = [];
  final ScrollController _scrollController = ScrollController();
  bool _isDownloading = false;
  String locationStatus = "Checking location...";
  bool _insideTrain = false;
  bool trainLoading = false;
  bool loadingOnbordingDetails = false;
  String insideTrainNumber = "";
  String insideTrainDate = "";
  List<String> insideTraincoachNumbers = [];
  EditTrainsData? trainData;
  List<int> frequency = [];
  String userType = "";
  int? returnGapDays;
  bool _showInsideTrainToggle = false;
  bool newInsideTrain = false;
  bool _isTogglingTrainStatus = false;

  // app update
  AppUpdateInfo? _updateInfo;
  bool _flexibleUpdateAvailable = false;

  List<String> fetchCoachNumbers() {
    if (insideTrainNumber == selectedTrain &&
        DateTime.parse(insideTrainDate) == selectedDate) {
      return insideTraincoachNumbers;
    } else {
      return coachNumbers;
    }
  }

  @override
  void initState() {
    super.initState();
    _checkForUpdate();
    final userModel = Provider.of<UserModel>(context, listen: false);
    token = userModel.token;
    userType = userModel.userType;
    _setInitialDataFromUserModel();
    // _requestPermissions();
    _fetchTrainNumbers();
    checkLocationStatus();
    _checkAndShowChangeEmailModal(token!);
    _fetchInsideTrainStatus();
    if (selectedTrain != null) {
      fetchChartingTime();
    }
    fetchInsideTrainDetails();
    _fetchInsideTrainStatus();
  }

  void _setInitialDataFromUserModel() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final initialTrainNo = userModel.trainNo ?? '';
    final initialSelectedDate = userModel.selectedDate ?? '';
    if (initialSelectedDate.isNotEmpty) {
      try {
        setState(() {
          selectedDate = DateTime.parse(initialSelectedDate);
        });
        _updateDateController();
      } catch (e) {
        print('Error parsing initial date: $e');
      }
    }

    if (initialTrainNo.isNotEmpty) {
      setState(() {
        selectedTrain = initialTrainNo;
      });
      _updateTrainNo(initialTrainNo);
    }
  }

  void _updateDateController() {
    if (selectedDate != null) {
      _dateController.text = _formatDateToCustomFormat(selectedDate!);
    }
  }

  void _updateTrainNo(String trainNumber) async {
    if (trainNumber != null) {
      setState(() {
        selectedTrain = trainNumber;
      });
      final userModel = Provider.of<UserModel>(context, listen: false);
      userModel.setTrainNo(trainNumber);
      await fetchInsideTrainDetails();
      await _fetchInsideTrainStatus();

      if (selectedDate != null) {
        _fetchAttendanceStations(trainNumber);
        fetchAttendance();
        fetchChartingTime();
        _fetchOnboardingDetails(
            trainNumber, DateFormat('yyyy-MM-dd').format(selectedDate));
        fetchReturnGapDay(trainNumber);
      }
    }
  }

  String _formatDateToCustomFormat(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month = DateFormat('MMM').format(date);
    final year = date.year.toString();
    return '$day-$month-$year';
  }

  bool _isSameDate(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  void _requestPermissions() async {
    final permissionHandler = PermissionHandlerService();
    await permissionHandler.requestLocationPermission(context);
    if (await Permission.storage.request().isDenied) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Storage permission is required to download files')),
      );
    }
  }

  void _toggleInsideTrainStatus(bool value) async {
    try {
      setState(() {
        _isTogglingTrainStatus = true;
      });

      print("Toggle Inside Train Status: $value");

      // print(
      //     "Selected Train: $selectedTrain, Inside Train Number: $insideTrainNumber, Inside Train Date: $insideTrainDate, Selected Date: $selectedDate, Same date: ${_isSameDate(DateTime.parse(insideTrainDate), selectedDate)} , inside train status: $_insideTrain, new inside train status: $newInsideTrain");

      if (selectedTrain == null || selectedTrain!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select a train first')),
        );
        return;
      }

      if ((_insideTrain == true) &&
          (insideTrainNumber.isNotEmpty && insideTrainDate.isNotEmpty) &&
          (insideTrainNumber != selectedTrain ||
              !_isSameDate(DateTime.parse(insideTrainDate), selectedDate))) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Please disable the inside train selected on $insideTrainDate for train $insideTrainNumber first',
            ),
            duration: Duration(seconds: 6),
          ),
        );
        return;
      }
      if (value == true) {
        await TrainService.addTrainDetails(
            trainNumber: selectedTrain!,
            coachNumbers: [],
            originDate: DateFormat('yyyy-MM-dd').format(selectedDate!),
            token: token!);
      }

      await ProfileTrainServices.toggleInsideTrain(
        token!,
        value,
        selectedTrain ?? insideTrainNumber,
      );

      print('Inside Train Status Updated: $value');

      setState(() {
        newInsideTrain = value;
        _insideTrain = newInsideTrain;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(value
                ? 'You are now marked as inside the train'
                : 'You are now marked as outside the train')),
      );
    } catch (e) {
      setState(() {
        newInsideTrain = _insideTrain;
      });
      print('Error updating train status: $e');
    } finally {
      // Refresh inside train details after toggling
      setState(() {
        _isTogglingTrainStatus = false;
      });
      fetchInsideTrainDetails();
      _fetchInsideTrainStatus();
    }
  }

  void _calculateToggleVisibility() {
    print("entering _calculateToggleVisibility");
    if (startTime == "NA" || trainData == null) return;
    print("startTime: $startTime, trainData: $trainData");
    try {
      final now = DateTime.now();
      final selectedDateStr = DateFormat('yyyy-MM-dd').format(selectedDate);
      final startDateTime =
          DateFormat('yyyy-MM-dd HH:mm').parse('$selectedDateStr $startTime');

      // Show toggle 10 minutes before start time
      setState(() {
        // _showInsideTrainToggle =
        //     now.isAfter(startDateTime.subtract(Duration(minutes: 10)));
        _showInsideTrainToggle = true;
        ;
      });
      print('Show Inside Train Toggle: $_showInsideTrainToggle');
    } catch (e) {
      print('Error parsing start time: $e');
    }
  }

  Future<int?> fetchReturnGapDay(String trainId) async {
    try {
      final tempReturnGapData =
          await ReturnGapService.fetchReturnGapByTrain(trainId);
      setState(() {
        returnGapDays = tempReturnGapData?.days;
      });
      print('Return Gap Days: $returnGapDays');
      return returnGapDays;
    } catch (e) {
      print('Error fetching return gap days: $e');
      setState(() {
        returnGapDays = null;
      });
      return null;
    }
  }

  bool isArrivalWithinBufferTime(String stationName, EditTrainsData trainData,
      int diffTime, String startEndTime) {
    if (startEndTime == null || startEndTime == "") {
      return false;
    }
    try {
      final now = DateTime.now();
      final arrivalTimeStr = startEndTime;
      DateTime arrival;
      // print(lastStationLocationFetched);
      final parts = arrivalTimeStr.split(":");
      if (lastStationLocationFetched) {
        DateTime futureBase;
        if (returnGapDays != null) {
          futureBase = selectedDate.add(Duration(days: returnGapDays!));
          print("Return Gap Days: $returnGapDays");
        } else {
          futureBase = selectedDate.add(Duration(days: journeyDays - 1));
        }
        arrival = DateTime(
          futureBase.year,
          futureBase.month,
          futureBase.day,
          int.parse(parts[0]),
          int.parse(parts[1]),
        );
      } else {
        arrival = DateTime(
          now.year,
          now.month,
          now.day,
          int.parse(parts[0]),
          int.parse(parts[1]),
        );
      }
      final difference = (arrival.difference(now).inMinutes).abs();
      // print("Station: $stationName , Difference : $difference");
      return difference >= 0 && difference <= diffTime;
    } catch (e) {
      return false;
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    if (trainNumber != null) return;

    setState(() {
      selectedTrain = trainNumber;
    });
    fetchInsideTrainDetails();
    final userModel = Provider.of<UserModel>(context, listen: false);
    userModel.setTrainNo(trainNumber!);

    if (selectedDate != null) {
      _fetchAttendanceStations(trainNumber);
      fetchAttendance();
      fetchChartingTime();
      _fetchOnboardingDetails(
          trainNumber, DateFormat('yyyy-MM-dd').format(selectedDate));
      fetchReturnGapDay(trainNumber);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _showForceUpdateDialog() async {
    if (!mounted) return;

    return SystemChannels.platform
        .invokeMethod<void>('SystemNavigator.preventPopInvokeMethod')
        .then((_) {
      if (!mounted) return;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => WillPopScope(
          onWillPop: () async => false,
          child: AlertDialog(
            title: const Text('Update Required'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.system_update, size: 50, color: Colors.blue),
                SizedBox(height: 16),
                Text(
                  'A new version of the app is available. You must update to continue using the app.',
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () async {
                  try {
                    await InAppUpdate.performImmediateUpdate();
                  } catch (e) {
                    if (mounted) {
                      _showForceUpdateDialog();
                    }
                  }
                },
                child: const Text('Update Now'),
              ),
            ],
          ),
        ),
      );
    });
  }

  Future<void> _downloadAttendanceDetails() async {
    if (selectedTrain == null || selectedDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a train number and date.'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    setState(() {
      _isDownloading = true;
    });

    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final userToken = userModel.token;

      // Fetch all necessary data
      await Future.wait([
        updateStations(selectedTrain!), // Stoppages
        _fetchTrainDetails(selectedTrain!), // Timings
        _fetchAttendanceStations(selectedTrain!), // Attendance stoppages
        fetchAttendance(), // Attendance counts
        fetchChartingTime(), // Charting times
        _fetchOnboardingDetails(selectedTrain!,
            DateFormat('yyyy-MM-dd').format(selectedDate)), // Passenger chart
        fetchReturnGapDay(selectedTrain!), // Return gap days
        _saveTrainLocation(), // Location update
        checkLocationStatus(), // Location status
      ] as Iterable<Future>);

      // Update UserModel with selected train and date
      userModel.setTrainNo(selectedTrain!);
      userModel.setSelectedDate(
          "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}");

      // Scroll to the nearest onboarding station
      if (onboardingStations.isNotEmpty) {
        int middleIndex = (onboardingStations.length / 2).floor();
        String nearestStation = onboardingStations[middleIndex];
        scrollToCurrentStation(nearestStation);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('All details updated successfully.'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(
      //     content: Text('Failed to update details: $e'),
      //     backgroundColor: Colors.red,
      //     duration: Duration(seconds: 3),
      //   ),
      // );
    } finally {
      setState(() {
        _isDownloading = false;
      });
    }
  }

  Future<void> checkLocationStatus() async {
    try {
      bool isLocationEnabled = await Geolocator.isLocationServiceEnabled();
      setState(() {
        locationStatus =
            isLocationEnabled ? "Location is ON" : "Location is OFF";
      });
    } catch (e) {
      setState(() {
        locationStatus = "Error checking location: $e";
      });
    }
  }

  void scrollToCurrentStation(String currentStation) {
    if (stations.isEmpty) return;

    final stationIndex = stations.indexOf(currentStation);
    if (stationIndex != -1) {
      final scrollPosition = stationIndex * 100.0;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController.animateTo(
          scrollPosition,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  Future<void> _fetchAttendanceStations(String trainNo) async {
    try {
      final stationAttendanceList =
          await EditTrainServices.fetchStoppagesForAttendanceByTrainNo(
              trainNo, token!);
      if (stationAttendanceList != null) {
        setState(() {
          attendanceStations =
              stationAttendanceList.attendanceStations ?? ['N/A'];
        });
      }
    } catch (e) {
      print('Error fetching train details: $e');
    }
  }

  Future<void> _checkForUpdate() async {
    try {
      AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();
      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        await _showForceUpdateDialog();
        return;
      }

      try {
        updateInfo = await InAppUpdate.checkForUpdate();
        if (updateInfo.updateAvailability ==
            UpdateAvailability.updateAvailable) {
          setState(() {
            _updateInfo = updateInfo;
            _flexibleUpdateAvailable = true;
          });
        }
      } catch (e) {
        print("Flexible update failed: $e");
      }
    } catch (e) {
      print("Error checking for update: $e");
    }
  }

  Future<void> _startFlexibleUpdate() async {
    if (_updateInfo != null && _flexibleUpdateAvailable) {
      try {
        await InAppUpdate.startFlexibleUpdate();
      } catch (e) {
        print("Error starting flexible update: $e");
      }
    }
  }

  Future<void> _checkAndShowChangeEmailModal(String token) async {
    final prefs = await SharedPreferences.getInstance();
    final lastShownTime = prefs.getInt('lastChangeEmailModalTime');
    final currentTime = DateTime.now().millisecondsSinceEpoch;

    if (lastShownTime == null ||
        currentTime - lastShownTime >= 2 * 60 * 60 * 1000) {
      final profileResponse = await ProfileService.getProfile(token);
      final email = await profileResponse.user?.email ?? '';
      _showChangeEmailModalOnce(email);

      prefs.setInt('lastChangeEmailModalTime', currentTime);
    }
  }

  Future<void> _showChangeEmailModalOnce(String email) async {
    if (email.startsWith("noemail")) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          builder: (BuildContext context) => ChangeEmailModal(),
        );
      });
    }
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      setState(() {
        trainLoading = true;
      });
      _trainNumbers = await TrainService.getTrainNumbers();
      setState(() {
        trainLoading = false;
      });
    } catch (e) {
      print('Error fetching train numbers: $e');
      setState(() {
        trainLoading = false;
      });
    }
  }

  Future<void> _fetchOnboardingDetails(String trainNumber, String date) async {
    try {
      onboardingResponse =
          await OnboardingService.fetchOnboardingDetails(trainNumber, date);
      setState(() {
        onboardingResponse = onboardingResponse;
        onboardingStations = onboardingResponse.stations ?? [];
        coachNumbers = onboardingResponse.coachNumbers ?? [];
      });
    } catch (e) {
      setState(() {
        onboardingResponse = OnboardingResponse(
            message: '',
            stations: [],
            lastLocationFetched: null,
            date: null,
            trainNumber: null,
            coachNumbers: [],
            details: {},
            detailsOffBoarding: {},
            detailsOffBoardingInroute: {},
            detailsInroute: {});
        onboardingStations = [];
      });
      print('Error fetching onboarding details: $e');
    }
  }

  Map<String, List<int>>? getStationOnboardingDetails(String stationCode) {
    return onboardingResponse.details?[stationCode];
  }

  Map<String, List<int>>? getStationOffboardingDetails(String stationCode) {
    return onboardingResponse.detailsOffBoarding?[stationCode];
  }

  Map<String, List<int>>? getStationVacantDetails(String stationCode) {
    return onboardingResponse.detailsVacant?[stationCode];
  }

  Map<String, List<int>>? getStationonboardingDetailsInroute(
      String stationCode) {
    return onboardingResponse.detailsInroute?[stationCode];
  }

  Map<String, List<int>>? getStationoffboardingDetailsInroute(
      String stationCode) {
    return onboardingResponse.detailsOffBoardingInroute?[stationCode];
  }

  Future<void> _fetchTrainDetails(String trainNo) async {
    try {
      final trainDetails =
          await EditTrainServices.fetchTrainDetailsByTrainNo(trainNo);
      if (trainDetails != null) {
        setState(() {
          startTime = trainDetails.startTime ?? 'NA';
          endTime = trainDetails.endTime ?? 'NA';
          journeyDays = trainDetails.journeyDurationDays ?? 1;
          trainData = trainDetails;
          frequency = trainDetails.frequency ?? [];
        });
      }
    } catch (e) {
      print('Error fetching train details: $e');
    }
  }

  String formatTimeinHHMM(String time) {
    List<String> parts = time.split(":");
    if (parts.length >= 2) {
      return "${parts[0]}:${parts[1]}";
    }
    return time;
  }

  Future<void> updateStations(String trainNumber) async {
    final result = await TrainService.getTrainStations(trainNumber);
    List<String> _stations = result['stationList'];
    final Map<String, String> _stationNames = result['stationsDict'];
    setState(() {
      stationNames = _stationNames;
      stations = _stations;
    });
  }

  Future<void> _selectDate() async {
    final DateTime today = DateTime.now();
    DateTime tempSelectedDate = selectedDate ?? today;

    DateTime? _selectedDate = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setStateDialog) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: const ColorScheme.light(
                  primary: Colors.blue,
                  onPrimary: Colors.white,
                  onSurface: Colors.black,
                ),
              ),
              child: AlertDialog(
                contentPadding: EdgeInsets.zero,
                content: Container(
                  width: 320,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Select date',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Text(
                                  DateFormat('EEE, MMM d')
                                      .format(tempSelectedDate),
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontSize: 32,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                const Spacer(),
                                IconButton(
                                  onPressed: () {},
                                  icon: const Icon(Icons.edit, size: 20),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const Divider(height: 1),
                      Container(
                        height: 320,
                        child: CalendarDatePicker(
                          initialDate: tempSelectedDate,
                          key: ValueKey(tempSelectedDate),
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                          currentDate: today,
                          onDateChanged: (DateTime date) {
                            setStateDialog(() {
                              tempSelectedDate = date;
                            });
                          },
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                        child: Row(
                          children: [
                            TextButton(
                              onPressed: () {
                                setStateDialog(() {
                                  tempSelectedDate = today;
                                });
                              },
                              child: const Text(
                                'Today',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const Spacer(),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: const Text(
                                'Cancel',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ),
                            const SizedBox(width: 8),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(tempSelectedDate);
                              },
                              child: const Text(
                                'OK',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );

    if (tempSelectedDate != null && tempSelectedDate != selectedDate) {
      setState(() {
        selectedDate = tempSelectedDate;
      });

      if (selectedTrain != null) {
        fetchAttendance();
        fetchChartingTime();
        _fetchOnboardingDetails(
            selectedTrain!, DateFormat('yyyy-MM-dd').format(selectedDate));
        fetchReturnGapDay(selectedTrain!);
        _calculateToggleVisibility();
        _fetchInsideTrainStatus();
        fetchInsideTrainDetails();
      }

      Provider.of<UserModel>(context, listen: false).setSelectedDate(
          "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}");
    }
  }

  void fetchAttendance() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    try {
      final attendanceCount =
          await AttendanceService.fetchStationAttendanceCount(
              trainNumber: selectedTrain!,
              date: DateFormat('yyyy-MM-dd').format(selectedDate),
              token: userToken);

      setState(() {
        stationAttendanceCount = attendanceCount;
      });

      print('Attendance Count: $attendanceCount');
    } catch (e) {
      print('Error: $e');
    }
  }

  void fetchChartingTime() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    try {
      final chartResponse = await TrainService.fetchChartingTime(
          trainNumber: selectedTrain,
          date: DateFormat('yyyy-MM-dd').format(selectedDate),
          token: userToken);
      setState(() {
        chartData = chartResponse;
        depot = chartResponse!.depot;
        ehkName = chartResponse.ehkDict;
        trainScheduledAt = chartResponse.startTime;
        refreshedTimes = chartResponse.refreshedTimes;
        expectedChartingTime = chartResponse.chartingTime;
        loadedAtEarliest = chartResponse.loadedAtEarliest;
        loadedAtRecent = chartResponse.loadedAtRecent;
        isRunningDay = chartResponse.isRunningDay;
      });
      _fetchTrainDetails(selectedTrain!);
      _showTrainStatusModal(isRunningDay);
      fetchReturnGapDay(selectedTrain!);
      //print(chartResponse!.chartingTime);
      _calculateToggleVisibility();
    } catch (e) {
      print('Error: $e');
    }
  }

  void _showTrainStatusModal(bool isRunning) {
    String dayOfWeek = DateFormat('EEEE').format(selectedDate);
    if (!isRunning) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.red,
            title: const Text(
              "Train Not Running",
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 16.0,
                  fontWeight: FontWeight.bold),
            ),
            content: Text(
              "Train *$selectedTrain* is *NOT* running on *$dayOfWeek*\nRunning Days: ${_formatRunningDays()}",
              style: const TextStyle(color: Colors.black, fontSize: 15.0),
            ),
            actions: <Widget>[
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade100,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'OK',
                  style: TextStyle(
                      fontSize: 14.0,
                      color: Colors.blue,
                      fontWeight: FontWeight.bold),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    }
  }

  Future<void> fetchInsideTrainDetails() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final userToken = userModel.token == null
        ? prefs.getString('authToken')
        : userModel.token;
    try {
      final trainDetails =
          await AttendanceService.fetchInsideTrainDetails(userToken);
      setState(() {
        selectedTrain = trainDetails["insideTrainNumber"];
        selectedDate = DateTime.parse(trainDetails["date"]);
        insideTrainNumber = trainDetails["insideTrainNumber"];
        selectedTrain = trainDetails["insideTrainNumber"];
        insideTrainDate = trainDetails["date"];
        insideTraincoachNumbers = trainDetails["coachNumbers"];
      });
      if (trainDetails["insideTrainNumber"] != null &&
          trainDetails["date"] != null) {
        fetchAttendance();
        updateStations(trainDetails["insideTrainNumber"]);
        _fetchTrainDetails(trainDetails["insideTrainNumber"]);
        _fetchOnboardingDetails(
            trainDetails["insideTrainNumber"], trainDetails["date"]);
        _fetchAttendanceStations(trainDetails["insideTrainNumber"]);
        fetchChartingTime();
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  Future<void> _fetchInsideTrainStatus() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final userToken = userModel.token == null
        ? prefs.getString('authToken')
        : userModel.token;
    try {
      print('Fetching inside train status for token: $userToken');
      final trainDetails =
          await AttendanceService.fetchInsideTrainDetails(userToken);
      final insideTrainStatus =
          await ProfileTrainServices.getInsideTrainStatus(userToken!);

      setState(() {
        _insideTrain = insideTrainStatus['inside_train'];
        // insideTrainNumber = insideTrainStatus['inside_train_number'] ?? "";
        // insideTrainDate = insideTrainStatus['inside_train_date'] ?? "";
      });
    } catch (e) {
      print('Error fetching toggle switch statuses: $e');
    }
  }

  Future<void> _handleRefresh() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;

    setState(() => _isDownloading = true);

    try {
      if (selectedTrain != null) {
        await _saveTrainLocation();
        fetchAttendance();
        await _fetchOnboardingDetails(
          selectedTrain!,
          DateFormat('yyyy-MM-dd').format(selectedDate),
        );
        await _fetchTrainDetails(selectedTrain!);
        await _fetchAttendanceStations(selectedTrain!);
        fetchChartingTime();
        await checkLocationStatus();
        await _saveTrainLocation();
        fetchReturnGapDay(selectedTrain!);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Data refreshed successfully')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Refresh failed: ${e.toString()}')),
      );
    } finally {
      setState(() => _isDownloading = false);
    }
  }

  Future<void> _saveTrainLocation() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    setState(() {
      _isDownloading = true;
    });
    try {
      Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      await LocationService.saveTrainLocation(
        userToken,
        position.latitude.toString(),
        position.longitude.toString(),
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Train Location Saved Successfully')),
      );
    } catch (e) {
      print('Error refreshing data: $e');
    } finally {
      fetchInsideTrainDetails();
      setState(() {
        _isDownloading = false;
      });
    }
  }

  bool _showDetails = false;

  String? getArrivalTimeForStation(
      String stationName, EditTrainsData trainData) {
    final arrival = trainData.arrivalTime?.firstWhere(
        (map) => map["station_code"] == stationName,
        orElse: () => {});

    return arrival != null && arrival.isNotEmpty
        ? arrival["arrival_time"]
        : null;
  }

  Widget _buildLastFetchedTime() {
    String formattedTime = "";
    String? locationFetchedFromUser;
    if (onboardingResponse.lastLocationFetched == null) {
      formattedTime = "location not fetched";
    } else {
      final lastFetched = onboardingResponse.lastLocationFetched!;
      final istTime =
          lastFetched.toUtc().add(const Duration(hours: 5, minutes: 30));
      formattedTime = DateFormat('dd-MM-yyyy HH:mm').format(istTime);
      lastFetchedFormattedDate = DateFormat('dd-MM-yyyy').format(istTime);
      lastStationLocationFetched = onboardingResponse.stations != null &&
          onboardingResponse.stations!.isNotEmpty &&
          stations.isNotEmpty &&
          onboardingResponse.stations!.contains(stations[stations.length - 1]);
    }

    (onboardingResponse.lastLocationFetchedFromUser == null)
        ? locationFetchedFromUser = "NA"
        : locationFetchedFromUser =
            (onboardingResponse.lastLocationFetchedFromUser);

    return Positioned(
      left: 5,
      bottom: 5,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          'Last fetched: $formattedTime\nFrom User: $locationFetchedFromUser',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  String formatTime(DateTime ogTime) {
    final istTime = ogTime.toUtc().add(const Duration(hours: 5, minutes: 30));
    String formattedTime = DateFormat('dd-MM-yyyy HH:mm').format(istTime);
    return formattedTime;
  }

  Widget _buildChartingTime() {
    String formattedTime = "";
    String? locationFetchedFromUser;
    if (onboardingResponse.lastLocationFetched == null) {
      formattedTime = "location not fetched";
    } else {
      final lastFetched = onboardingResponse.lastLocationFetched!;
      formattedTime = formatTime(lastFetched);
    }

    return Positioned(
      left: 5,
      top: 110,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _showDetails = !_showDetails;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.5),
            borderRadius: BorderRadius.circular(2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _showDetails
              ? Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(width: 4),
                    const SizedBox(width: 4),
                    Text(
                      'Expected Charting Time: $trainScheduledAt',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Charting started at: ${loadedAtEarliest != "NA" ? formatTime(DateTime.parse(loadedAtEarliest)) : "NA"}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Charting refreshed at: ${loadedAtRecent != "NA" ? formatTime(DateTime.parse(loadedAtRecent)) : "NA"}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    SizedBox(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(
                          maxHeight: 100,
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            children: refreshedTimes.map((time) {
                              return Row(
                                children: [
                                  const SizedBox(width: 4),
                                  Text(
                                    'Charting Time: $time',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                    Text(
                      'Train Depot:$depot',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'EHK Assigned for train:$ehkName',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'User Location:$locationStatus',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                )
              : const Icon(
                  Icons.access_time,
                  size: 14,
                  color: Colors.white,
                ),
        ),
      ),
    );
  }

  String _formatRunningDays() {
    if (frequency.isEmpty) return 'N/A';

    final dayAbbreviations = frequency.map((dayIndex) {
      final dayName =
          DateFormat('EEEE').format(DateTime(2023, 1, 1 + dayIndex));
      return dayName.substring(0, 3);
    }).toList();

    if (dayAbbreviations.length == 7) return 'Daily';

    return dayAbbreviations.join(', ');
  }

  @override
  Widget build(BuildContext context) {
    if (_flexibleUpdateAvailable) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startFlexibleUpdate();
      });
    }
    return Scaffold(
      appBar: const CustomAppBar(title: "Attendance"),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: Stack(
          children: [
            Column(
              children: [
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                      vertical: 8.0, horizontal: 12.0),
                  decoration: const BoxDecoration(
                    color: Color.fromRGBO(239, 239, 239, 1),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: DropdownSearch<String>(
                          popupProps: PopupProps.menu(
                              showSearchBox: true,
                              searchFieldProps: TextFieldProps(
                                keyboardType: TextInputType.number,
                                inputFormatters: <TextInputFormatter>[
                                  FilteringTextInputFormatter.digitsOnly,
                                  LengthLimitingTextInputFormatter(5),
                                ],
                              )),
                          items: _trainNumbers,
                          dropdownDecoratorProps: const DropDownDecoratorProps(
                            dropdownSearchDecoration: InputDecoration(
                              labelText: "Train Number",
                              labelStyle: TextStyle(fontSize: 14),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 12),
                            ),
                          ),
                          dropdownButtonProps: DropdownButtonProps(
                            icon: trainLoading
                                ? const Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2),
                                    ),
                                  )
                                : const Icon(Icons.arrow_drop_down),
                          ),
                          onChanged: (value) async {
                            selectedTrain = value;
                            updateStations(value!);
                            _fetchTrainDetails(value);
                            if (value != null && selectedDate != null) {
                              _fetchAttendanceStations(value);
                              fetchAttendance();
                              fetchChartingTime();
                              _fetchOnboardingDetails(
                                  selectedTrain!,
                                  DateFormat('yyyy-MM-dd')
                                      .format(selectedDate));
                              await Provider.of<UserModel>(context,
                                      listen: false)
                                  .setTrainNo(value);
                            }
                          },
                          selectedItem: selectedTrain,
                        ),
                      ),
                      if (_insideTrain)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Image.asset(
                            'assets/images/user_inside_train.png',
                            width: 50,
                            height: 50,
                          ),
                        ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: "Date",
                              labelStyle: TextStyle(fontSize: 14),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 12),
                            ),
                            child: Text(
                              "${selectedDate.day.toString().padLeft(2, '0')}-${DateFormat('MMM').format(selectedDate)}-${selectedDate.year}",
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  color: Colors.blueGrey.shade200,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 20),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Timings",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 12),
                            ),
                            SizedBox(
                              height: 5,
                              width: 25,
                            ),
                            Text(
                              "Stoppages",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(right: 20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              "Passenger Chart   Atten..",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Container(
                    color: const Color.fromRGBO(239, 239, 239, 1),
                    child: ListView.builder(
                      controller: _scrollController,
                      itemCount: stations.length,
                      itemBuilder: (context, index) {
                        final station = stations[index];
                        final arrivalTime = trainData != null
                            ? getArrivalTimeForStation(station, trainData!)
                            : null;
                        bool isAttendanceAllowed = false;
                        if ((stations.length - 1) == index || index == 0) {
                          isAttendanceAllowed = true;
                        } else {
                          isAttendanceAllowed =
                              attendanceStations.contains(station);
                        }

                        final hasOnboardingStations =
                            onboardingStations.isNotEmpty;
                        DateTime? trainStartDateTime;
                        try {
                          final selectedDateStr =
                              DateFormat('yyyy-MM-dd').format(selectedDate);
                          trainStartDateTime = DateFormat('yyyy-MM-dd HH:mm')
                              .parse('$selectedDateStr $startTime');
                        } catch (e) {
                          print('Error parsing start time: $e');
                        }

                        final hasTrainStarted = trainStartDateTime != null &&
                            DateTime.now().isAfter(trainStartDateTime);

                        final isOnboardingStation = hasOnboardingStations
                            ? onboardingStations.contains(station)
                            : (hasTrainStarted && index == 0);

                        final firstOnboardingIndex = stations
                            .indexWhere((s) => onboardingStations.contains(s));

                        final isBeforeOnboarding = firstOnboardingIndex != -1 &&
                            index < firstOnboardingIndex;
                        final isAfterOnboarding = firstOnboardingIndex != -1 &&
                            index > firstOnboardingIndex;
                        final isInTwoHourBuffer = trainData != null
                            ? isArrivalWithinBufferTime(
                                station, trainData!, 12 * 60, startTime)
                            : false;
                        final isInSixHourBuffer = trainData != null
                            ? isArrivalWithinBufferTime(
                                stations[stations.length - 1],
                                trainData!,
                                12 * 60,
                                endTime)
                            : false;
                        final isInThirySixHourBuffer = trainData != null
                            ? isArrivalWithinBufferTime(
                                stations[stations.length - 1],
                                trainData!,
                                36 * 60,
                                endTime,
                              )
                            : false;
                        bool isFirstStaion =
                            (stations[index] == stations[0]) ? true : false;
                        bool isLastStaion =
                            (stations[index] == stations[stations.length - 1])
                                ? true
                                : false;
                        bool isFirstAndLastStation =
                            (stations[index] == stations[0] ||
                                    stations[index] ==
                                        stations[stations.length - 1])
                                ? true
                                : false;

                        return StationItem(
                          userType: userType,
                          isInTwoHourBuffer: isInTwoHourBuffer,
                          isInSixHourBuffer: isInSixHourBuffer,
                          isInThirySixHourBuffer: isInThirySixHourBuffer,
                          isFirstAndLastStation: isFirstAndLastStation,
                          isAttendanceStation: isAttendanceAllowed,
                          arrivalTime: arrivalTime,
                          stationCode: station,
                          isFirstStaion: isFirstStaion,
                          isLastStaion: isLastStaion,
                          stationName: stationNames[station] ?? "station name",
                          attendanceCount: stationAttendanceCount[station] ?? 0,
                          showStartTime: index == 0,
                          showEndTime: index == stations.length - 1,
                          startTime: formatTimeinHHMM(startTime),
                          endTime: formatTimeinHHMM(endTime),
                          selectedTrain: selectedTrain!,
                          selectedDate: DateFormat('yyyy-MM-dd')
                              .format(selectedDate)
                              .toString(),
                          isOnboardingStation: isOnboardingStation,
                          onboardingDetails:
                              getStationOnboardingDetails(station),
                          offboardingDetails:
                              getStationOffboardingDetails(station),
                          vacantDetails: getStationVacantDetails(station),
                          isBeforeOnboarding: isBeforeOnboarding,
                          isAfterOnboarding: isAfterOnboarding,
                          coachNumbers: insideTrainNumber == selectedTrain &&
                                  DateTime.parse(insideTrainDate) ==
                                      selectedDate
                              ? insideTraincoachNumbers
                              : coachNumbers,
                          onboardingDetailsInroute:
                              getStationonboardingDetailsInroute(station),
                          offboardingDetailsInroute:
                              getStationoffboardingDetailsInroute(station),
                          nearbyStations: onboardingStations,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
            _buildLastFetchedTime(),
            _buildChartingTime(),
          ],
        ),
      ),
      floatingActionButton: Container(
        padding: const EdgeInsets.only(right: 12, bottom: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Toggle Button (Inside/Go Train)
            if ([
              "railway admin",
              "war room user",
              "s2 admin",
              "railway officer"
            ].contains(userType))
              ElevatedButton(
                onPressed: _isTogglingTrainStatus
                    ? null
                    : () {
                        _toggleInsideTrainStatus(!newInsideTrain);
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      _insideTrain ? Colors.green : Colors.red[300],
                  foregroundColor: Colors.white,
                  elevation: 6,
                  shadowColor: Colors.black26,
                  shape: const CircleBorder(),
                  padding: EdgeInsets.zero,
                  fixedSize: const Size(40, 40),
                ),
                child: _isTogglingTrainStatus
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _insideTrain ? 'Inside' : 'Go',
                            style: const TextStyle(fontSize: 8),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            _insideTrain ? 'Train' : 'Inside',
                            style: const TextStyle(fontSize: 8),
                          ),
                          if (!_insideTrain) const SizedBox(height: 2),
                          if (!_insideTrain)
                            const Text(
                              'Train',
                              style: TextStyle(fontSize: 8),
                            ),
                        ],
                      ),
              ),

            const SizedBox(width: 12),

            // Update FAB
            FloatingActionButton.small(
              onPressed: _isDownloading
                  ? null
                  : _insideTrain
                      ? _downloadAttendanceDetails
                      : [
                          "railway admin",
                          "war room user",
                          "s2 admin",
                          "railway officer"
                        ].contains(userType)
                          ? () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'You are not inside the train. Please go inside the train first.'),
                                ),
                              );
                            }
                          : _downloadAttendanceDetails,
              backgroundColor: _isDownloading
                  ? Colors.grey
                  : _insideTrain
                      ? Colors.blueGrey.shade200
                      : Colors.grey.shade400,
              child: _isDownloading
                  ? const SizedBox(
                      width: 14,
                      height: 14,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 1.5,
                      ),
                    )
                  : const Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.location_on, size: 20, color: Colors.black),
                        Text(
                          "Update",
                          style: TextStyle(fontSize: 10, color: Colors.black),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
    );
  }
}

Widget _buildTimeText(String? time) {
  return Text(
    time ?? "-",
    style: const TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.bold,
    ),
    textAlign: TextAlign.right,
  );
}

class StationItem extends StatelessWidget {
  final String stationCode;
  final String stationName;
  final int attendanceCount;
  final bool showStartTime;
  final bool showEndTime;
  final String startTime;
  final String endTime;
  final String selectedDate;
  final String selectedTrain;
  final bool isOnboardingStation;
  final bool isInTwoHourBuffer;
  final Map<String, List<int>>? onboardingDetails;
  final Map<String, List<int>>? offboardingDetails;
  final Map<String, List<int>>? vacantDetails;
  final bool isAttendanceStation;
  final bool isBeforeOnboarding;
  final bool isAfterOnboarding;
  final bool isInSixHourBuffer;
  final bool isInThirySixHourBuffer;
  final bool isFirstAndLastStation;
  final bool isLastStaion;
  final bool isFirstStaion;
  final List<String> coachNumbers;
  final String? arrivalTime;
  final Map<String, List<int>>? offboardingDetailsInroute;
  final Map<String, List<int>>? onboardingDetailsInroute;
  final List<String>? nearbyStations;
  final String userType;

  const StationItem(
      {Key? key,
      required this.stationCode,
      required this.stationName,
      required this.attendanceCount,
      this.showStartTime = false,
      this.showEndTime = false,
      required this.startTime,
      required this.isInTwoHourBuffer,
      required this.isFirstAndLastStation,
      required this.userType,
      required this.endTime,
      required this.selectedDate,
      required this.selectedTrain,
      required this.isOnboardingStation,
      required this.offboardingDetails,
      required this.onboardingDetails,
      required this.vacantDetails,
      required this.isAttendanceStation,
      required this.isBeforeOnboarding,
      required this.isAfterOnboarding,
      required this.coachNumbers,
      required this.isInSixHourBuffer,
      required this.isInThirySixHourBuffer,
      required this.isLastStaion,
      required this.isFirstStaion,
      this.arrivalTime,
      this.nearbyStations,
      required this.offboardingDetailsInroute,
      required this.onboardingDetailsInroute})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    Map<String, Map<String, int>> coachTotals = {};
    Map<String, Map<String, int>> filteredCoachTotals = {};
    Set<String> allCoaches = {
      ...?onboardingDetails?.keys,
      ...?offboardingDetails?.keys,
      ...?vacantDetails?.keys,
    };
    for (String coach in allCoaches) {
      coachTotals[coach] = {
        'onboard': onboardingDetails?[coach]?.length ?? 0,
        'offboard': offboardingDetails?[coach]?.length ?? 0,
        'vacant': vacantDetails?[coach]?.length ?? 0,
      };

      if (coachNumbers.contains(coach)) {
        filteredCoachTotals[coach] = coachTotals[coach]!;
      }
    }

    var filteredEntries = coachTotals.entries
        .where((entry) =>
            entry.value['onboard']! > 0 ||
            entry.value['offboard']! > 0 ||
            entry.value['vacant']! > 0)
        .toList();

    final totalOnboard = filteredEntries
        .map((e) => e.value['onboard']!)
        .fold<int>(0, (sum, count) => sum + count);
    final totalOffboard = filteredEntries
        .map((e) => e.value['offboard']!)
        .fold<int>(0, (sum, count) => sum + count);
    final totalVacant = filteredEntries
        .map((e) => e.value['vacant']!)
        .fold<int>(0, (sum, count) => sum + count);

    filteredEntries = coachTotals.entries
        .where((entry) => coachNumbers.contains(entry.key))
        .toList();

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(width: 10),
        SizedBox(
          width: 80,
          child: Column(
            children: [
              if (showStartTime) _buildTimeText(startTime),
              if (!showStartTime)
                _buildTimeText(
                    arrivalTime?.isNotEmpty == true ? arrivalTime : " "),
              if (showEndTime) _buildTimeText(endTime),
              if (!showEndTime &&
                  !showStartTime &&
                  (arrivalTime == null || arrivalTime!.isEmpty))
                _buildTimeText("-"),
            ],
          ),
        ),
        const SizedBox(width: 10),
        Column(
          children: [
            Stack(
              alignment: showStartTime
                  ? Alignment.topCenter
                  : showEndTime
                      ? Alignment.bottomCenter
                      : Alignment.center,
              children: [
                Container(
                  width: 14,
                  height: showStartTime || showEndTime ? 90 : 100,
                  margin: EdgeInsets.only(
                    top: showStartTime ? 8 : 0,
                    bottom: showEndTime ? 8 : 0,
                  ),
                  decoration: BoxDecoration(
                    color: const Color.fromARGB(255, 198, 223, 239),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(showStartTime ? 8 : 0),
                      topRight: Radius.circular(showStartTime ? 8 : 0),
                      bottomLeft: Radius.circular(showEndTime ? 8 : 0),
                      bottomRight: Radius.circular(showEndTime ? 8 : 0),
                    ),
                  ),
                ),
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.green,
                          width: 2,
                        ),
                      ),
                    ),
                    if (isOnboardingStation)
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                    if (!isOnboardingStation && isBeforeOnboarding)
                      const Icon(
                        Icons.check,
                        size: 12,
                        color: Colors.green,
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
        const SizedBox(width: 30),
        Expanded(
          child: InkWell(
            onTapDown: (details) async {
              if ((isAttendanceStation &&
                  nearbyStations!.contains(stationCode))) {
                if (!(isFirstAndLastStation)) {
                  final result = await showMenu<String>(
                    context: context,
                    position: RelativeRect.fromLTRB(
                      details.globalPosition.dx,
                      details.globalPosition.dy,
                      details.globalPosition.dx,
                      details.globalPosition.dy,
                    ),
                    items: [
                      const PopupMenuItem(
                        value: 'self',
                        child: Text('Self'),
                      ),
                      const PopupMenuItem(
                        value: 'coach attendent',
                        child: Text('Other CA'),
                      ),
                      const PopupMenuItem(
                        value: 'OBHS',
                        child: Text('Other EHK/OBHS'),
                      ),
                    ],
                  );

                  if (result != null) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ImageUploadPage(
                            selectedOption: result == "self" ? "self" : "other",
                            forUserType: result,
                            trainNumber: selectedTrain,
                            journeyDate: selectedDate,
                            stationCode: stationCode,
                            stationName: stationName),
                      ),
                    );
                  }
                } else {
                  if ((isInTwoHourBuffer ||
                      isInSixHourBuffer ||
                      (userType == "war room user" &&
                          isInThirySixHourBuffer))) {
                    final result = await showMenu<String>(
                      context: context,
                      position: RelativeRect.fromLTRB(
                        details.globalPosition.dx,
                        details.globalPosition.dy,
                        details.globalPosition.dx,
                        details.globalPosition.dy,
                      ),
                      items: [
                        const PopupMenuItem(
                          value: 'self',
                          child: Text('Self'),
                        ),
                        const PopupMenuItem(
                          value: 'coach attendent',
                          child: Text('Other CA'),
                        ),
                        const PopupMenuItem(
                          value: 'OBHS',
                          child: Text('Other EHK/OBHS'),
                        ),
                      ],
                    );

                    if (result != null) {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ImageUploadPage(
                              selectedOption:
                                  result == "self" ? "self" : "other",
                              forUserType: result,
                              trainNumber: selectedTrain,
                              journeyDate: selectedDate,
                              stationCode: stationCode,
                              stationName: stationName),
                        ),
                      );
                    }
                  } else {
                    String bufferMessage = "";
                    if ((!isInSixHourBuffer && isLastStaion) &&
                        (isInThirySixHourBuffer && isLastStaion)) {
                      bufferMessage =
                          "The train, Train Number: $selectedTrain is not within 12 hour buffer after reaching the last station."
                          "Attendance can only be marked by a war room user.";
                    } else if (!isInTwoHourBuffer && isFirstStaion) {
                      bufferMessage =
                          "The train, Train Number: $selectedTrain is not within 12 hour buffer";
                    } else if ((!isInSixHourBuffer && isLastStaion) &&
                        (!isInThirySixHourBuffer && isLastStaion)) {
                      bufferMessage =
                          "Train Number: $selectedTrain has exceeded the 36-hour buffer after reaching the last station. Attendance cannot be marked.";
                    } else {
                      bufferMessage =
                          "The time between the current time and the train's arrival time is not within the buffer.\n"
                          "If you're unable to see the arrival time correctly, please contact the admin or add the arrival time for train.";
                    }

                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                      content: Text(bufferMessage),
                      backgroundColor: const Color.fromARGB(255, 237, 162, 76),
                      duration: const Duration(seconds: 10),
                    ));
                  }
                }
              } else {
                if (!isAttendanceStation) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                          "Attendance cannot be marked for station $stationCode as it is not an attendance station."),
                      backgroundColor: const Color.fromARGB(255, 237, 162, 76),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                } else if (!nearbyStations!.contains(stationCode)) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                          "You're over 50 KM away from the selected station $stationCode. Attendance can only be marked when you're within the allowed range.\nFor Now You can only mark attendance for stations: ${nearbyStations!.join(', ')}"),
                      backgroundColor: const Color.fromARGB(255, 237, 162, 76),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                } else if (!isInTwoHourBuffer && isFirstStaion) {
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                    content: Text(
                        "The time between the current time and the train's arrival time is not within the 2-hour buffer.\n"
                        "If you're unable to see the arrival time, please contact the admin or add the arrival time for this train."),
                    backgroundColor: Color.fromARGB(255, 237, 162, 76),
                    duration: Duration(seconds: 2),
                  ));
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                          "Train Location is not fetched yet, please try again later"),
                      backgroundColor: const Color.fromARGB(255, 237, 162, 76),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    stationCode,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isOnboardingStation
                          ? Colors.green.shade700
                          : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    stationName.length > 10
                        ? stationName.substring(0, 11)
                        : stationName,
                    style: TextStyle(
                      fontSize: 12,
                      color: isOnboardingStation
                          ? Colors.green.shade600
                          : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            if (totalOnboard == 0 && totalOffboard == 0 && totalVacant == 0) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Chart has not been prepared for this station'),
                  backgroundColor: Colors.orange,
                ),
              );
            } else {
              showDialog(
                context: context,
                builder: (BuildContext context) => StationDetailsPopup(
                  stationCode: stationCode,
                  onboardingDetails: onboardingDetails,
                  offboardingDetails: offboardingDetails,
                  vacantDetails: vacantDetails,
                  coachTotals: coachTotals,
                  filteredCoachTotals: filteredCoachTotals,
                  coachNumbers: coachNumbers,
                  onboardingDetailsInroute: onboardingDetailsInroute,
                  offboardingDetailsInroute: offboardingDetailsInroute,
                ),
              );
            }
          },
          child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.transparent),
              ),
              child: Column(children: [
                Table(
                  defaultColumnWidth: const IntrinsicColumnWidth(),
                  border: TableBorder.all(
                    color: Colors.white,
                    width: 2,
                  ),
                  children: [
                    TableRow(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                      ),
                      children: [
                        _buildTableCell('Total',
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? Colors.blue.shade200
                                : Colors.blue.shade50,
                            isOnboardingStation: isOnboardingStation),
                        _buildTableCell(totalOnboard.toString(),
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? Colors.green.shade200
                                : Colors.green.shade50,
                            isOnboardingStation: isOnboardingStation),
                        _buildTableCell(totalOffboard.toString(),
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? const Color.fromARGB(255, 237, 162, 76)
                                : const Color.fromARGB(255, 252, 231, 174),
                            isOnboardingStation: isOnboardingStation),
                        _buildTableCell(totalVacant.toString(),
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? Colors.grey.shade300
                                : Colors.grey.shade100,
                            isOnboardingStation: isOnboardingStation),
                      ],
                    ),
                    ...filteredEntries.take(3).map((entry) {
                      return TableRow(
                        children: [
                          _buildTableCell(entry.key,
                              backgroundColor: isOnboardingStation
                                  ? Colors.blue.shade200
                                  : Colors.blue.shade50,
                              isOnboardingStation: isOnboardingStation),
                          _buildTableCell(entry.value['onboard'].toString(),
                              backgroundColor: isOnboardingStation
                                  ? Colors.green.shade200
                                  : Colors.green.shade50,
                              isOnboardingStation: isOnboardingStation),
                          _buildTableCell(entry.value['offboard'].toString(),
                              backgroundColor: isOnboardingStation
                                  ? const Color.fromARGB(255, 237, 162, 76)
                                  : const Color.fromARGB(255, 252, 231, 174),
                              isOnboardingStation: isOnboardingStation),
                          _buildTableCell(entry.value['vacant'].toString(),
                              backgroundColor: isOnboardingStation
                                  ? Colors.grey.shade200
                                  : Colors.grey.shade100,
                              isOnboardingStation: isOnboardingStation),
                        ],
                      );
                    }),
                  ],
                ),
                if (filteredEntries.length > 3)
                  Container(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      'click for more...',
                      style: TextStyle(
                        fontSize: 8,
                        height: 0.5,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
              ])),
        ),
        if (isAttendanceStation)
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AttendanceDetailsPage(
                      trainNumber: selectedTrain,
                      date: selectedDate,
                      stationCode: stationCode),
                ),
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                "A: $attendanceCount",
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ),
          ),
        const SizedBox(width: 20),
      ],
    );
  }

  Widget _buildTableCell(String text,
      {bool isBold = false,
      Color? backgroundColor,
      bool isOnboardingStation = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      color: backgroundColor,
      child: Text(
        text,
        style: TextStyle(
            fontSize: 10,
            fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
            color: isOnboardingStation ? Colors.black : Colors.grey.shade900),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class StationDetailsPopup extends StatefulWidget {
  final String stationCode;
  final Map<String, List<int>>? onboardingDetails;
  final Map<String, List<int>>? offboardingDetails;
  final Map<String, List<int>>? vacantDetails;
  final Map<String, Map<String, int>>? coachTotals;
  final Map<String, Map<String, int>>? filteredCoachTotals;
  final List<String> coachNumbers;
  final Map<String, List<int>>? offboardingDetailsInroute;
  final Map<String, List<int>>? onboardingDetailsInroute;

  const StationDetailsPopup(
      {Key? key,
      required this.stationCode,
      this.onboardingDetails,
      this.offboardingDetails,
      this.vacantDetails,
      this.coachTotals,
      this.filteredCoachTotals,
      required this.coachNumbers,
      this.offboardingDetailsInroute,
      this.onboardingDetailsInroute})
      : super(key: key);

  @override
  State<StationDetailsPopup> createState() => _StationDetailsPopupState();
}

class _StationDetailsPopupState extends State<StationDetailsPopup> {
  bool showSleeperCoaches = false;

  bool isSleeperCoach(String coach) {
    return coach.toLowerCase().startsWith('s');
  }

  Map<String, List<int>> filterSleeperCoaches(
      Map<String, List<int>> details, bool wantSleeper) {
    return Map.fromEntries(
      details.entries
          .where((entry) => isSleeperCoach(entry.key) == wantSleeper),
    );
  }

  Map<String, List<int>> _filterDetailsByCoaches(Map<String, List<int>> details,
      List<String> coachNumbers, bool includeSleeper) {
    return Map.fromEntries(
      details.entries.where((entry) =>
          coachNumbers.contains(entry.key) &&
          isSleeperCoach(entry.key) == includeSleeper),
    );
  }

  Map<String, List<int>> _excludeDetailsByCoaches(
      Map<String, List<int>> details,
      List<String> coachNumbers,
      bool includeSleeper) {
    return Map.fromEntries(
      details.entries.where((entry) =>
          !coachNumbers.contains(entry.key) &&
          isSleeperCoach(entry.key) == includeSleeper),
    );
  }

  bool isInrouteBerth(String coach, int berth) {
    final onboardingInroute = widget.onboardingDetailsInroute ?? {};
    final offboardingInroute = widget.offboardingDetailsInroute ?? {};

    return (onboardingInroute.containsKey(coach) &&
            onboardingInroute[coach]!.contains(berth)) ||
        (offboardingInroute.containsKey(coach) &&
            offboardingInroute[coach]!.contains(berth));
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                      vertical: 0.0, horizontal: 0.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(width: 40),
                      Expanded(
                        child: Text(
                          'Station ${widget.stationCode} Details',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
                const Divider(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      showSleeperCoaches = !showSleeperCoaches;
                    });
                  },
                  child: Text(
                    showSleeperCoaches
                        ? 'Back to AC Coaches'
                        : 'Show Sleeper Coaches',
                    style: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w600),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 0.0),
                  child: Text(
                    showSleeperCoaches ? 'Sleeper Coaches' : 'AC coaches',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(
                      'Onboarding Details (Your Coaches)',
                      Colors.green,
                      _filterDetailsByCoaches(widget.onboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.filteredCoachTotals,
                    ),
                    _buildSection(
                      'Onboarding Details (All Coaches)',
                      Colors.green,
                      _excludeDetailsByCoaches(widget.onboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.coachTotals,
                    ),
                    _buildSection(
                      'Offboarding Details (Your Coaches)',
                      const Color.fromARGB(255, 237, 162, 76),
                      _filterDetailsByCoaches(widget.offboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.filteredCoachTotals,
                    ),
                    _buildSection(
                      'Offboarding Details (All Coaches)',
                      const Color.fromARGB(255, 237, 162, 76),
                      _excludeDetailsByCoaches(widget.offboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.coachTotals,
                    ),
                    _buildSection(
                      'Vacant Details (Your Coaches)',
                      Colors.grey.shade600,
                      _filterDetailsByCoaches(widget.vacantDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.filteredCoachTotals,
                    ),
                    _buildSection(
                      'Vacant Details (All Coaches)',
                      Colors.blue,
                      _excludeDetailsByCoaches(widget.vacantDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.coachTotals,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, Color color,
      Map<String, List<int>> details, Map<String, Map<String, int>>? totals) {
    if (details.isEmpty) return const SizedBox.shrink();

    Color backgroundColor =
        HSLColor.fromColor(color).withLightness(0.95).toColor();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        _buildDetailsTable(details, totals, backgroundColor),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildDetailsTable(
    Map<String, List<int>> details,
    Map<String, Map<String, int>>? coachTotals,
    Color backgroundColor,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: backgroundColor,
      ),
      child: Table(
        columnWidths: const {
          0: IntrinsicColumnWidth(),
          1: FlexColumnWidth(),
        },
        border: TableBorder.all(
          color: Colors.grey.shade300,
          borderRadius: BorderRadius.circular(8),
        ),
        children: [
          TableRow(
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
            ),
            children: [
              _buildTableCell('Coach', backgroundColor, isHeader: true),
              _buildTableCell('Berths', backgroundColor, isHeader: true),
            ],
          ),
          ...details.entries
              .where((entry) => entry.value.isNotEmpty)
              .map((entry) => _buildDataRow(entry, coachTotals))
              .toList(),
        ],
      ),
    );
  }

  TableRow _buildDataRow(MapEntry<String, List<int>> entry,
      Map<String, Map<String, int>>? coachTotals) {
    final berthCount = entry.value.length;

    if (berthCount > 18) {
      return TableRow(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(entry.key,
                    style: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Text('${coachTotals?[entry.key]?['onboard'] ?? 0}',
                    style: const TextStyle(color: Colors.green)),
                Text('${coachTotals?[entry.key]?['offboard'] ?? 0}',
                    style: const TextStyle(
                        color: Color.fromARGB(255, 237, 162, 76))),
                Text('${coachTotals?[entry.key]?['vacant'] ?? 0}',
                    style: TextStyle(color: Colors.grey.shade600)),
              ],
            ),
          ),
          _buildBerthsList(entry.key, entry.value),
        ],
      );
    }

    return TableRow(
      children: [
        _buildCoachCell(entry.key, coachTotals),
        _buildBerthsList(entry.key, entry.value),
      ],
    );
  }

  Widget _buildTableCell(String text, backgroundColor,
      {bool isHeader = false}) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
      alignment: Alignment.center,
      child: Text(
        text,
        style: TextStyle(
          fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildCoachCell(
      String coach, Map<String, Map<String, int>>? coachTotals) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(coach,
              style:
                  const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
          Row(
            children: [
              Text('${coachTotals?[coach]?['onboard'] ?? 0} - ',
                  style: const TextStyle(color: Colors.green, fontSize: 13)),
              Text('${coachTotals?[coach]?['offboard'] ?? 0} - ',
                  style: const TextStyle(
                      color: Color.fromARGB(255, 237, 162, 76), fontSize: 13)),
              Text('${coachTotals?[coach]?['vacant'] ?? 0}',
                  style: const TextStyle(color: Colors.grey, fontSize: 13)),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildBerthsList(String coach, List<int> berths) {
    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: 4,
        runSpacing: 4,
        children: berths.map((berth) {
          final isInroute = isInrouteBerth(coach, berth);
          return Text(
            isInroute ? "(${berth})," : "${berth},",
            style: TextStyle(
              fontSize: 13,
              fontWeight: isInroute ? FontWeight.bold : FontWeight.normal,
            ),
          );
        }).toList(),
      ),
    );
  }
}
