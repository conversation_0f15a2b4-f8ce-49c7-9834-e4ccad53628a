import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/auth_model.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/types/profile_types/profile_response.dart';
import 'widgets/add_train_form.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:railops/types/profile_types/add_trains_response.dart';
import 'package:railops/services/api_services/index.dart';
import 'package:railops/services/assign_ehk_ca_services/assign_ehk_ca_services.dart';

class AddTrainScreen extends StatefulWidget {
  const AddTrainScreen({Key? key}) : super(key: key);

  @override
  _AddTrainScreenState createState() => _AddTrainScreenState();
}

class _AddTrainScreenState extends State<AddTrainScreen> {
  List<AddTrainDetail> _ongoingTrainDetails = [];
  List<AddTrainDetail> _nonOngoingTrainDetails =
      []; // Non-ongoing train details
  bool _isLoading = false;
  bool _hasFetchedDetails = false;

  @override
  void initState() {
    super.initState();
    _fetchTrainDetails();
  }

  Future<void> _fetchTrainDetails() async {
    if (!mounted) return;
    if (_hasFetchedDetails) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;
      final response = await TrainService.getTrainDetails(token);

      setState(() {
        _ongoingTrainDetails = response.trainDetails.values.toList();
        _nonOngoingTrainDetails =
            _flattenList(response.nonOngoingTrains.values.toList());
        _hasFetchedDetails = true;
      });
    } catch (e) {
      print('Error fetching train details: $e');
      if (e is ApiException && e.statusCode == 401) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Navigator.pushReplacementNamed(context, Routes.login);
        });
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<AddTrainDetail> _flattenList(List<List<AddTrainDetail>> listOfLists) {
    return listOfLists.expand((list) => list).toList();
  }

  Future<void> _deleteTrain(String trainNumber) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;
      await TrainService.deleteTrainDetails(token, trainNumber);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text('Train $trainNumber details deleted successfully')),
      );
    } catch (e) {
      print('Error deleting train details: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
      Navigator.pushNamed(context, '/add-train-profile');
    }
  }

  Future<void> removeTrainDetailsOriginDate(
      List<String> coachNumbers, String trainNumber, String dateParam) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    setState(() {
      _isLoading = true;
    });

    String forUserType = "";
    String userType = userModel.userType;
    if (userType == "coach attendant") {
      forUserType = "CA";
    } else if (userType == "EHK") {
      forUserType = "EHK";
    } else if (userType == "OBHS") {
      forUserType = "OBHS";
    }

    await AdminAssignService.removeTrainDetailsOriginDate(userModel.userName,
        coachNumbers, trainNumber, dateParam, forUserType, token);

    setState(() {
      _isLoading = false;
    });
    Navigator.pushNamed(context, '/add-train-profile');
  }

  Future<void> _reloadPage() async {
    await Future.delayed(const Duration(seconds: 3));
    reloadCurrentScreen(context, const AddTrainScreen());
  }

  void reloadCurrentScreen(BuildContext context, Widget currentScreen) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => currentScreen,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'Add Train'),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: _reloadPage,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                FutureBuilder(
                  future: Provider.of<AuthModel>(context, listen: false)
                      .loadAuthState(),
                  builder: (context, AsyncSnapshot<void> snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const CircularProgressIndicator();
                    } else if (snapshot.connectionState ==
                        ConnectionState.done) {
                      final authModel =
                          Provider.of<AuthModel>(context, listen: false);
                      if (authModel.isAuthenticated) {
                        return AddTrainForm();
                      } else {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          Navigator.pushReplacementNamed(context, Routes.login);
                        });
                        return const CircularProgressIndicator();
                      }
                    } else if (snapshot.hasError) {
                      return const Text('Error loading authentication state');
                    }
                    return const CircularProgressIndicator();
                  },
                ),
                const SizedBox(height: 16),
                _isLoading
                    ? const CircularProgressIndicator()
                    : _ongoingTrainDetails.isNotEmpty ||
                            _nonOngoingTrainDetails.isNotEmpty
                        ? Column(
                            children: [
                              _buildOngoingTrainTable(),
                              const SizedBox(height: 16),
                              _buildNonOngoingTrainTable(),
                            ],
                          )
                        : const Text('No train details available.'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Ongoing train details table
  Widget _buildOngoingTrainTable() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recent Train Details',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            dataRowMaxHeight: double.infinity,
            columnSpacing: 16,
            border: TableBorder.all(color: Colors.black, width: 1),
            columns: const [
              DataColumn(
                label: Text(
                  'Train',
                  softWrap: true,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    overflow: TextOverflow.visible,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Coaches',
                  softWrap: true,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    overflow: TextOverflow.visible,
                  ),
                ),
              ),
              DataColumn(
                label: Text(
                  'Origin Date',
                  softWrap: true,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    overflow: TextOverflow.visible,
                  ),
                ),
              ),
            ],
            rows: _ongoingTrainDetails
                .map(
                  (detail) => DataRow(
                    cells: [
                      DataCell(Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: Text(
                              detail.trainNumber,
                              softWrap: true,
                              style:
                                  const TextStyle(overflow: TextOverflow.visible),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () => removeTrainDetailsOriginDate(
                                detail.coachNumbers,
                                detail.trainNumber,
                                detail.originDate),
                          ),
                        ],
                      )),
                      DataCell(
                        Text(
                          (detail.coachNumbers == null || detail.coachNumbers.isEmpty)
                              ? 'N/A'
                              : detail.coachNumbers.join(', '),
                          softWrap: true,
                          style: const TextStyle(overflow: TextOverflow.visible),
                        ),
                      ),
                      DataCell(
                        Text(
                          detail.originDate,
                          softWrap: true,
                          style: const TextStyle(overflow: TextOverflow.visible),
                        ),
                      ),
                    ],
                  ),
                )
                .toList(),
          ),
        ),
      ],
    );
  }

  // Non-Ongoing train details table
  Widget _buildNonOngoingTrainTable() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_nonOngoingTrainDetails.isNotEmpty) ...[
          const Text(
            'Older Train Details',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(
              dataRowMaxHeight: double.infinity,
              columnSpacing: 16,
              border: TableBorder.all(color: Colors.black, width: 1),
              columns: const [
                DataColumn(
                  label: Text(
                    'Train',
                    softWrap: true,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Coaches',
                    softWrap: true,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                ),
                DataColumn(
                  label: Text(
                    'Origin Date',
                    softWrap: true,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                ),
              ],
              rows: _nonOngoingTrainDetails
                  .map(
                    (detail) => DataRow(
                      cells: [
                        DataCell(Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Flexible(
                              child: Text(
                                detail.trainNumber,
                                softWrap: true,
                                style:
                                    const TextStyle(overflow: TextOverflow.visible),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => removeTrainDetailsOriginDate(
                                  detail.coachNumbers,
                                  detail.trainNumber,
                                  detail.originDate),
                            ),
                          ],
                        )),
                        DataCell(
                          Text(
                            (detail.coachNumbers == null || detail.coachNumbers.isEmpty)
                                ? 'N/A'
                                : detail.coachNumbers.join(', '),
                            softWrap: true,
                            style: const TextStyle(overflow: TextOverflow.visible),
                          ),
                        ),
                        DataCell(
                          Text(
                            detail.originDate,
                            softWrap: true,
                            style:
                                const TextStyle(overflow: TextOverflow.visible),
                          ),
                        ),
                      ],
                    ),
                  )
                  .toList(),
            ),
          ),
        ] else ...[
          const SizedBox.shrink(), // Empty space if no rows exist
        ],
      ],
    );
  }
}