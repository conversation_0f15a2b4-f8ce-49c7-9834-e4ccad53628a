import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class NewMobileNumberField extends StatelessWidget {
  final void Function(String) onSaved;
  final void Function(String) onChanged;
  final String? labelText;
  final bool isOptional;
  final List<TextInputFormatter>? inputFormatters;

  const NewMobileNumberField({
    super.key,
    required this.onSaved,
    required this.onChanged,
    this.labelText,
    this.isOptional = false,
    this.inputFormatters,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      keyboardType: TextInputType.phone,
      inputFormatters: inputFormatters,
      decoration: InputDecoration(
        labelText: labelText ?? 'Mobile Number *',
        hintText: isOptional
            ? 'Enter your mobile number (Optional)'
            : 'Enter your mobile number',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.grey,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.grey,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.blue,
            width: 2.0,
          ),
        ),
      ),
      validator: (value) {
        // If the field is optional and empty, it's valid
        if (isOptional && (value == null || value.isEmpty)) {
          return null;
        }

        // For required fields or if a value is provided
        if (value == null || value.isEmpty) {
          return 'Please enter your mobile number';
        }
        if (!RegExp(r'^[0-9]{10}$').hasMatch(value)) {
          return 'Please enter a valid 10-digit mobile number';
        }
        return null;
      },
      onSaved: (String? value) {
        onSaved(value ?? '');
      },
      onChanged: onChanged,
    );
  }
}
