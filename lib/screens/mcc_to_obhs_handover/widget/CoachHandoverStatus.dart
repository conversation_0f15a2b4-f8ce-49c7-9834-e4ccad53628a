import 'package:flutter/material.dart';

class CoachHandoverStatus extends StatelessWidget {
  final String status;
  final DateTime? timestamp;

  const CoachHandoverStatus({
    Key? key,
    required this.status,
    this.timestamp,
  }) : super(key: key);

  Color _getStatusColor() {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (status.toLowerCase()) {
      case 'completed':
        return Icons.check_circle;
      case 'pending':
        return Icons.pending;
      case 'failed':
        return Icons.error;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatusColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _getStatusColor()),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getStatusIcon(),
            color: _getStatusColor(),
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            status,
            style: TextStyle(
              color: _getStatusColor(),
              fontWeight: FontWeight.bold,
            ),
          ),
          if (timestamp != null) ...[
            const SizedBox(width: 8),
            Text(
              'at ${timestamp!.hour}:${timestamp!.minute.toString().padLeft(2, '0')}',
              style: TextStyle(
                color: _getStatusColor(),
                fontSize: 12,
              ),
            ),
          ],
        ],
      ),
    );
  }
} 