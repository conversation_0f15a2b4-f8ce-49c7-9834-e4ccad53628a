import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/add_user/widget/new_user_email_field.dart';
import 'package:railops/screens/update_user/widget/update_user_email_field.dart';
import 'package:railops/screens/update_user/widget/update_user_mobile_field.dart';
import 'package:railops/screens/update_user/widget/update_user_whatsapp_field.dart';
import 'package:railops/screens/update_user/widget/update_secondary_phone_field.dart';
import 'package:railops/screens/user_screen/widgets/index.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/signup_error.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/zone_dropdown.dart';
import 'package:railops/services/authentication_services/Request_update_service.dart';
import 'package:railops/services/authentication_services/auth_service.dart';
import 'package:railops/services/profile_services/profile_services.dart';
import 'package:railops/services/train_services/train_service_signup.dart';
import 'package:railops/services/user_info_services/user_info_services.dart';
import 'package:railops/types/train_types/zone_division_type.dart';
import 'package:railops/widgets/index.dart';

class UpdateUserFrom extends StatefulWidget {
  final Map<String, dynamic>? userData;
  const UpdateUserFrom({super.key, required this.userData});

  @override
  State<UpdateUserFrom> createState() => _UpdateUserFromState();
}

class _UpdateUserFromState extends State<UpdateUserFrom> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _empNumberController = TextEditingController();
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _middleNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _whatsappController = TextEditingController();
  final TextEditingController _secondaryPhoneController = TextEditingController();

  String? usertype;
  String? userName;
  String? token;
  int? userId;

  // Form field values
  String _role = 'coach attendent';
  String _password = '';
  String _rePassword = '';
  List<String> _selectedZones = [];
  List<String> _division = [];
  List<String> _depot = [];

  // Original values for comparison
  List<String> _originalZone = [];
  List<String> _originalDivision = [];
  List<String> _originalDepot = [];

  bool _isEmailFieldVisible = true;
  bool _isLoading = false;
  bool _sameAsPhone = false;
  bool _isDataLoaded = false;
  bool _isWhatsAppButtonEnabled = false;

  // Available options for dropdowns
  List<String> availableZones = [];
  List<String> availableDivisions = [];
  List<String> availableDepots = [];

  List<String> trainList = [];
  List<String> EmpNumberList = [];
  List<String> _trainNumber = [];
  List<String> _coachNumber = [];

  String? currentUserType;

  // Widget keys for stable widget instances
  final GlobalKey _zoneDropdownKey = GlobalKey();
  final GlobalKey _divisionDropdownKey = GlobalKey();
  final GlobalKey _depotDropdownKey = GlobalKey();

  // Cache control flags
  bool _shouldRebuildStaffFields = true;
  bool _shouldRebuildWhatsAppFields = true;
  bool _shouldRebuildEmailField = true;

  bool get _isPassenger => _role.toLowerCase() == 'passenger';

  // Define a consistent theme
  final _primaryColor = Colors.blue;
  final _secondaryColor = Colors.green.shade600;
  final _borderRadius = 10.0;
  final _fieldSpacing = 16.0;

  @override
  void initState() {
    super.initState();
    final userModel = Provider.of<UserModel>(context, listen: false);
    setState(() {
      currentUserType = userModel.userType;
    });
    if (widget.userData != null) {
      _loadInitialUserData(widget.userData!);
    }
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      token = userModel.token;
      usertype = userModel.userType;
      userName = userModel.userName;
    } catch (e) {
      print("Error accessing user model: $e");
    }
  }

  @override
  void dispose() {
    // Dispose all controllers
    _empNumberController.dispose();
    _firstNameController.dispose();
    _middleNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _mobileController.dispose();
    _whatsappController.dispose();
    _secondaryPhoneController.dispose();
    super.dispose();
  }

  // Optimized setState that prevents unnecessary rebuilds
  void _safeSetState(VoidCallback callback, {bool forceRebuild = false}) {
    if (!mounted) return;
    
    // Store previous values for comparison
    String previousRole = _role;
    List<String> previousZones = List.from(_selectedZones);
    List<String> previousDivisions = List.from(_division);
    List<String> previousDepots = List.from(_depot);
    bool previousSameAsPhone = _sameAsPhone;
    String previousWhatsapp = _whatsappController.text;
    String previousEmail = _emailController.text;

    setState(() {
      callback();
    });

    // Check what actually changed and update rebuild flags accordingly
    if (forceRebuild || previousRole != _role) {
      _shouldRebuildStaffFields = true;
      _shouldRebuildWhatsAppFields = true;
      _shouldRebuildEmailField = true;
    } else {
      // Check specific changes
      if (previousZones.toString() != _selectedZones.toString() ||
          previousDivisions.toString() != _division.toString() ||
          previousDepots.toString() != _depot.toString()) {
        _shouldRebuildStaffFields = true;
      }
      
      if (previousSameAsPhone != _sameAsPhone || 
          previousWhatsapp != _whatsappController.text) {
        _shouldRebuildWhatsAppFields = true;
      }
      
      if (previousEmail != _emailController.text) {
        _shouldRebuildEmailField = true;
      }
    }
  }

  void _loadInitialUserData(Map<String, dynamic> data) {
    _safeSetState(() {
      userId = int.tryParse(data['user_id']?.toString() ?? '');
      _isLoading = true;
    });

    String? whatsappNumber;
    String? secondaryPhoneNumber;

    // Check for secondary phone directly in the userData first
    data.forEach((key, value) {
      if (key.toLowerCase() == 'whatsapp_number') {
        whatsappNumber = value?.toString();
      }
      if (key.toLowerCase() == 'secondary_phone') {
        secondaryPhoneNumber = value?.toString();
      }
    });

    String? mobileNumber = data['phone']?.toString();
    String email = data['email'] ?? '';

    _firstNameController.text = data['first_name'] ?? '';
    _middleNameController.text = data['middle_name'] ?? '';
    _lastNameController.text = data['last_name'] ?? '';
    _emailController.text = email;
    _mobileController.text = mobileNumber ?? '';
    _empNumberController.text = data['emp_number'] ?? '';

    // Set secondary phone directly if available in userData
    if (secondaryPhoneNumber != null && secondaryPhoneNumber!.isNotEmpty) {
      _secondaryPhoneController.text = secondaryPhoneNumber!;
    }

    _safeSetState(() {
      // More precise WhatsApp number handling
      if (whatsappNumber == null || whatsappNumber!.isEmpty) {
        _sameAsPhone = true;
        _whatsappController.text = mobileNumber ?? '';
      } else {
        _sameAsPhone = whatsappNumber == mobileNumber;
        _whatsappController.text = whatsappNumber!;
      }

      _isWhatsAppButtonEnabled = true;

      //Set zone, division, and depot from user data with better parsing
      String zoneValue = data['zone']?.toString() ?? '';
      String divisionValue = data['division']?.toString() ?? '';
      String depotValue = data['depot']?.toString() ?? '';

      //print("DEBUG: Raw values from API - Zone: '$zoneValue', Division: '$divisionValue', Depot: '$depotValue'");

      _selectedZones = zoneValue.isNotEmpty ? [zoneValue] : [];
      _division = divisionValue.isNotEmpty ? [divisionValue] : [];
      _depot = depotValue.isNotEmpty ? [depotValue] : [];

      // Initialize available options with current values to ensure they show up
      availableZones = _selectedZones.isNotEmpty ? List.from(_selectedZones) : [];
      availableDivisions = _division.isNotEmpty ? List.from(_division) : [];
      availableDepots = _depot.isNotEmpty ? List.from(_depot) : [];

      // Store original values
      _originalZone = List.from(_selectedZones);
      _originalDivision = List.from(_division);
      _originalDepot = List.from(_depot);

      _role = data['user_type'] ?? _role;
    }, forceRebuild: true);

    // Get user info to extract secondary phone from API if not directly available
    if (data['access_token'] != null &&
        (secondaryPhoneNumber == null || secondaryPhoneNumber!.isEmpty)) {
      UserService.getUserInfo(data['access_token']).then((userInfoList) {
        if (userInfoList.isNotEmpty && mounted) {
          _safeSetState(() {
            _secondaryPhoneController.text = userInfoList[0].secondaryPhone;
          });
        }
      }).catchError((error) {
        print("Error fetching user info: $error");
      }).whenComplete(() {
        if (mounted) {
          _loadDependentDataSafely();
        }
      });
    } else {
      // Load dependent data after setting initial values
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _loadDependentDataSafely();
        }
      });
    }
  }

  Future<void> _loadDependentDataSafely() async {
    if (!mounted) return;

    _safeSetState(() => _isLoading = true);

    try {
      // Load all available zones first
      await _loadAllZones();

      // Load divisions for current zones and merge with existing
      if (_selectedZones.isNotEmpty) {
        await _loadDivisionsForZones(preserveExisting: true);
      }

      // Load depots for current divisions and merge with existing
      if (_division.isNotEmpty) {
        await _loadDepotsForDivision(preserveExisting: true);
      }

      if (mounted) {
        _safeSetState(() {
          _isDataLoaded = true;
          _isLoading = false;
        }, forceRebuild: true);
      }
    } catch (e) {
      if (mounted) {
        _safeSetState(() {
          _isLoading = false;
        });
        showErrorModal(context, "Error loading data: $e", "Error", () {});
      }
    }
  }

  Future<void> _loadAllZones() async {
    try {
      // Add current zones to available zones if they're not already there
      if (_selectedZones.isNotEmpty) {
        Set<String> zoneSet = Set.from(availableZones);
        zoneSet.addAll(_selectedZones);
        availableZones = zoneSet.toList();
      }
    } catch (e) {
      print("Error loading zones: $e");
    }
  }

  Future<void> _loadDivisionsForZones({bool preserveExisting = false}) async {
    if (_selectedZones.isEmpty) {
      if (!preserveExisting) {
        availableDivisions = [];
        _division = [];
        availableDepots = [];
        _depot = [];
      }
      return;
    }

    try {
      List<ZoneDivision> divisions = await TrainServiceSignup.getDivisions(_selectedZones.join(','));
      List<String> newDivisions = divisions.map((division) => division.code).toList();

      if (preserveExisting) {
        // Merge existing divisions with new ones
        Set<String> divisionSet = Set.from(availableDivisions);
        divisionSet.addAll(newDivisions);
        availableDivisions = divisionSet.toList();
      } else {
        availableDivisions = newDivisions;
      }

      // Keep only divisions that are available for the selected zones
      _division = _division.where((div) => availableDivisions.contains(div)).toList();

      // Load depots for remaining divisions
      if (_division.isNotEmpty) {
        await _loadDepotsForDivision(preserveExisting: preserveExisting);
      } else if (!preserveExisting) {
        availableDepots = [];
        _depot = [];
      }
    } catch (e) {
      print("Error loading divisions: $e");
    }
  }

  Future<void> _loadDepotsForDivision({bool preserveExisting = false}) async {
    if (_division.isEmpty) {
      if (!preserveExisting) {
        availableDepots = [];
        _depot = [];
      }
      return;
    }

    String divisionString = _division.join(',');
    if (divisionString.trim().isEmpty) {
      if (!preserveExisting) {
        availableDepots = [];
        _depot = [];
      }
      return;
    }

    try {
      final newDepots = await TrainServiceSignup.getDepot(divisionString);
      if (preserveExisting) {
        // Merge existing depots with new ones
        Set<String> depotSet = Set.from(availableDepots);
        depotSet.addAll(newDepots);
        availableDepots = depotSet.toList();
      } else {
        availableDepots = newDepots;
      }

      // Keep only depots that are available for the selected divisions
      _depot = _depot.where((dep) => availableDepots.contains(dep)).toList();
    } catch (e) {
      showErrorModal(context, 'Error loading depots: $e', "Error", () {});
    }
  }

  void _handleZoneSelection(String selectedZones) async {
    List<String> newZones = selectedZones.split(',').where((zone) => zone.isNotEmpty).toList();

    // Only update if zones actually changed
    if (newZones.toString() == _selectedZones.toString()) return;

    _safeSetState(() {
      _selectedZones = newZones;
    });

    if (_selectedZones.isEmpty) {
      availableDivisions = [];
      _division = [];
      availableDepots = [];
      _depot = [];
      _safeSetState(() {}, forceRebuild: true);
      return;
    }

    // Load divisions for the new zone selection without preserving existing
    await _loadDivisionsForZones(preserveExisting: false);
    if (mounted) {
      _safeSetState(() {}, forceRebuild: true);
    }
  }

  void _handleDivisionSelection(String selectedDivisions) async {
    List<String> newDivisions = selectedDivisions.split(',').where((div) => div.isNotEmpty).toList();

    // Only update if divisions actually changed
    if (newDivisions.toString() == _division.toString()) return;

    _safeSetState(() {
      _division = newDivisions;
    });

    // Load depots for the new division selection without preserving existing
    await _loadDepotsForDivision(preserveExisting: true);
    if (mounted) {
      _safeSetState(() {}, forceRebuild: true);
    }
  }

  void _handleDepotSelection(String selectedDepots) {
    List<String> newDepots = selectedDepots.split(',').where((dep) => dep.isNotEmpty).toList();

    // Only update if depots actually changed
    if (newDepots.toString() == _depot.toString()) return;

    _safeSetState(() {
      _depot = newDepots;
    });

    // Load train list if needed
    getTrainList();
  }

  Future getTrainList() async {
    // Implementation for getting train list
    // Uncomment and modify as needed
    /*
    if(_depot.isEmpty) return;
    try {
      final getData = await TrainServiceSignup.getTrainList(_depot.join(','));
      setState(() {
        trainList = getData['trains'];
        EmpNumberList = getData['emp_numbers'];
      });
    } catch (e) {
      showErrorModal(context, '$e', "Error", () {});
      if (e is StateError && e.toString().contains('mounted')) {
        print('Widget disposed before operation completes');
      } else {
        print('Send Otp Failed : $e');
      }
    }
    */
  }

  Future<void> _submitUpdateRequest() async {
    // First validate the form
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Save the form data
    _formKey.currentState!.save();

    // Validate phone ≠ secondary phone
    if (_mobileController.text.trim() == _secondaryPhoneController.text.trim()) {
      showErrorModal(
        context,
        'Phone number and Secondary phone number must be different.',
        "Validation Error",
        () {},
      );
      return;
    }

    // Check other required conditions
    if (_role != 'passenger') {
      if (_depot.isEmpty || _depot.every((element) => element.trim().isEmpty)) {
        showErrorModal(context, 'Please select at least one depot', "Error", () {});
        return;
      }
      if (userId == null) {
        showErrorModal(context, 'User ID is missing', "Error", () {});
        return;
      }
    }

    _safeSetState(() {
      _isLoading = true;
    });

    try {
      final Map<String, dynamic> updateData = {
        'user_id': userId,
        'phone': _mobileController.text,
        'f_name': _firstNameController.text,
        'm_name': _middleNameController.text,
        'l_name': _lastNameController.text,
        'email': _emailController.text,
        'user_type': _role,
        'division': _division.join(','),
        'depo': _depot.join(','),
        'zone': _selectedZones.join(','),
        'whatsapp_number': _whatsappController.text,
        'emp_number': _empNumberController.text,
        'secondary_phone': _secondaryPhoneController.text,
      };

      final response = await UpdateUserService.submitUpdateRequest(
        token!,
        updateData,
      );

      Navigator.of(context).pop();
      showSuccessModal(
        context,
        '${response['message']}',
        "Success",
        () => Navigator.pushNamed(context, Routes.userInfo),
      );
    } catch (e) {
      if (e is SignupDetailsException) {
        showErrorDialog(context, e.details);
      } else {
        showErrorModal(context, '${e}', "Error", () {});
      }
    } finally {
      _safeSetState(() {
        _isLoading = false;
      });
    }
  }

  // Create styled input decoration
  InputDecoration _getInputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      labelStyle: TextStyle(color: _primaryColor),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
        borderSide: BorderSide(color: _primaryColor, width: 2),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
        borderSide: BorderSide(color: Colors.grey.shade400),
      ),
      filled: true,
      fillColor: Colors.grey.shade50,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
    );
  }

  Widget _buildEmailField() {
    if (!_shouldRebuildEmailField) {
      return _cachedEmailField!;
    }

    //print("DEBUG: Rebuilding email field");
    _cachedEmailField = UpdateEmailField(
      initialValue: _emailController.text,
      onSaved: (value) {
        _emailController.text = value;
      },
    );
    _shouldRebuildEmailField = false;

    return _cachedEmailField!;
  }

  Widget? _cachedEmailField;

  Widget _buildStaffOnlyFields() {
    //print("DEBUG: _buildStaffOnlyFields called - Role: $_role, IsPassenger: $_isPassenger");

    if (_isPassenger) {
      return const SizedBox.shrink();
    }

    if (!_shouldRebuildStaffFields && _cachedStaffOnlyFields != null) {
      //print("DEBUG: Using cached staff fields");
      return _cachedStaffOnlyFields!;
    }

   // print("DEBUG: Rebuilding staff fields");

    _cachedStaffOnlyFields = Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(_borderRadius),
        border: Border.all(color: Colors.grey.shade300),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Text(
              'Staff Information',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),
          // Zone Dropdown
          ZoneDropdown(
            key: _zoneDropdownKey,
            onSaved: _handleZoneSelection,
            initialValue: _selectedZones.join(','),
          ),
          SizedBox(height: _fieldSpacing),

          // Division Dropdown
          DivisionDropdown(
            key: _divisionDropdownKey,
            divisions: availableDivisions,
            initialValue: _division.join(','),
            onSaved: _handleDivisionSelection,
          ),
          SizedBox(height: _fieldSpacing),

          // Depot Dropdown
          DepotDropdown(
            key: _depotDropdownKey,
            depots: availableDepots,
            initialValue: _depot.join(','),
            onSaved: _handleDepotSelection,
          ),
          SizedBox(height: _fieldSpacing),

          // Employee Number Field
          EmpNumberField(
            initialValue: _empNumberController.text,
            onSaved: (value) => _empNumberController.text = value,
            userType: _role,
            empNumberList: EmpNumberList.isEmpty && _empNumberController.text.isNotEmpty
                ? [_empNumberController.text]
                : EmpNumberList,
          ),
        ],
      ),
    );

    _shouldRebuildStaffFields = false;
    return _cachedStaffOnlyFields!;
  }

  Widget? _cachedStaffOnlyFields;

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: _primaryColor,
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: _primaryColor),
          SizedBox(height: 16),
          Text(
            'Loading user data...',
            style: TextStyle(color: _primaryColor, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _handlePhoneNumberChange(String value) {
    _safeSetState(() {
      _isWhatsAppButtonEnabled = true;
      if (_sameAsPhone) {
        _whatsappController.text = value;
      }
    });
  }

  Widget _buildWhatsAppFields() {
    if (!_shouldRebuildWhatsAppFields && _cachedWhatsAppFields != null) {
      return _cachedWhatsAppFields!;
    }

    //print("DEBUG: Rebuilding WhatsApp fields");

    _cachedWhatsAppFields = Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(_borderRadius),
        border: Border.all(color: Colors.grey.shade300),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          OutlinedButton(
            onPressed: _toggleWhatsAppSameAsPhone,
            style: OutlinedButton.styleFrom(
              foregroundColor: _sameAsPhone ? Colors.grey : Colors.blue[700],
              backgroundColor: _sameAsPhone
                  ? Colors.grey.withOpacity(0.1)
                  : Colors.blue.withOpacity(0.1),
              side: BorderSide(
                color: _sameAsPhone ? Colors.grey : Colors.blue[700]!,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'WhatsApp number same as phone number (${_sameAsPhone ? 'Yes' : 'No'})',
              style: TextStyle(
                fontSize: 14.0,
                color: _sameAsPhone ? Colors.grey : Colors.blue[700],
              ),
            ),
          ),
          SizedBox(height: _fieldSpacing),
          UpdateWhatsappNumberField(
            initialValue: _whatsappController.text,
            enabled: true,
            onSaved: (value) {
              _safeSetState(() {
                _whatsappController.text = value;
                _sameAsPhone = false;
              });
            },
            onChanged: (value) {
              _safeSetState(() {
                _whatsappController.text = value;
                _sameAsPhone = false;
              });
            },
          ),
        ],
      ),
    );
    
    _shouldRebuildWhatsAppFields = false;
    return _cachedWhatsAppFields!;
  }

  Widget? _cachedWhatsAppFields;

  void _toggleWhatsAppSameAsPhone() {
    _safeSetState(() {
      _sameAsPhone = !_sameAsPhone;

      if (_sameAsPhone) {
        _whatsappController.text = _mobileController.text;
        _isWhatsAppButtonEnabled = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double buttonWidth = screenWidth * 0.9;

    if (_isLoading && !_isDataLoaded) {
      return _buildLoadingIndicator();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(_borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  'Update User Profile',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
              ),
              const Divider(height: 32),
              
              // Personal Information Section
              _buildSectionTitle('Personal Information'),
              FirstNameField(
                initialValue: _firstNameController.text,
                onSaved: (value) => _firstNameController.text = value,
              ),
              SizedBox(height: _fieldSpacing),
              MiddleNameField(
                initialValue: _middleNameController.text,
                onSaved: (value) => _middleNameController.text = value,
              ),
              SizedBox(height: _fieldSpacing),
              LastNameField(
                initialValue: _lastNameController.text,
                onSaved: (value) => _lastNameController.text = value,
              ),
              SizedBox(height: _fieldSpacing),
              
              // Contact Information Section
              _buildSectionTitle('Contact Information'),
              _buildEmailField(),
              SizedBox(height: _fieldSpacing),
              UpdateMobileNumberField(
                initialValue: _mobileController.text,
                onSaved: (value) => _mobileController.text = value,
                onChanged: (value) {
                  if (_sameAsPhone && mounted) {
                    _whatsappController.text = value;
                  }
                  _handlePhoneNumberChange(value);
                },
              ),
              SizedBox(height: _fieldSpacing),
              UpdateSecondaryPhoneField(
                initialValue: _secondaryPhoneController.text,
                onSaved: (value) => _secondaryPhoneController.text = value,
                onChanged: (value) {
                  _safeSetState(() {
                    _secondaryPhoneController.text = value;
                  });
                },
              ),
               SizedBox(height: _fieldSpacing * 1.5),
              _buildSectionTitle('Role Information'),
               RolesDropdown(
                initialValue: _role,
                onSaved: (value) {
                  if (mounted) {
                    setState(() {
                      _role = value;
                      // Clear staff-only fields when switching to passenger role
                      if (_isPassenger) {
                        _selectedZones = [];
                        _division = [];
                        _depot = [];
                        _empNumberController.text = '';
                      }
                    });
                  }
                },
              ),
              SizedBox(height: _fieldSpacing),
              // Staff-only fields (Zone, Division, Depot, Employee Number)
              _buildStaffOnlyFields(),
              SizedBox(height: _fieldSpacing * 2),
              // Submit Button
              Container(
                alignment: Alignment.center,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _secondaryColor,
                    foregroundColor: Colors.white,
                    elevation: 2,
                    fixedSize: Size(buttonWidth, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: _submitUpdateRequest,
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : Text(
                          currentUserType == 'railway admin'
                              ? 'Update User'
                              : 'Request For Update User',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
              SizedBox(height: _fieldSpacing),
            ],
          ),
        ),
      ),
    );
  }
}