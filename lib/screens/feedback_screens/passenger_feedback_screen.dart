import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/feedback_screens/rm_feedback_screen.dart';
import 'package:railops/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/services/feedback_services/confirm_email.dart';
import 'package:railops/services/feedback_services/feedback_submission.dart';
import 'package:railops/services/feedback_services/verify_email.dart';
import 'package:railops/services/profile_services/pnr_service.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:railops/screens/feedback_screens/widgets/review_feedback.dart';
import 'package:railops/types/edit_train_types/edit_train_types.dart'
    show EditTrainsData;
import 'package:railops/types/train_types/train_charting_response.dart'
    show TrainChartingResponse;
import 'package:railops/widgets/index.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:railops/screens/pdf_screen/widgets/date_select.dart';

class StationPassengerFeedbackScreen extends StatefulWidget {
  const StationPassengerFeedbackScreen({Key? key}) : super(key: key);

  @override
  _StationPassengerFeedbackScreenState createState() =>
      _StationPassengerFeedbackScreenState();
}

class _StationPassengerFeedbackScreenState
    extends State<StationPassengerFeedbackScreen> {
  bool _isWidgetActive = true;
  bool isEmailVerified = false;
  bool isPNRVerified = false;
  bool showOtpInput = false;
  bool loading = false;
  bool isUploading = false;
  bool isLoadingTrainDetails = false;
  String? _selectedTrainNumber;
  String? _trainName;
  DateTime selectedDate = DateTime.now();
  int currentTimeInSeconds = DateTime.now().millisecondsSinceEpoch;
  String formattedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
  String taskStatus = 'pending';
  List<int?> feedbackRatings = [];
  String? selectedCategory = 'AC';
  List<String> stations = [];
  String startTime = "NA";
  String endTime = "NA";
  EditTrainsData? trainData;
  TrainChartingResponse? chartData;
  String trainScheduledAt = "NA";
  List<String> refreshedTimes = [];
  String expectedChartingTime = "NA";
  String loadedAtRecent = "NA";
  String loadedAtEarliest = "NA";
  bool isRunningDay = false;
  List<int> frequency = [];
  Map<String, EditTrainsData> trainDetailsMap = {};
  Map<String, TrainChartingResponse> chartDataMap = {};

  List<XFile> _selectedPNRFiles = [];
  List<XFile> _selectedFeedbackFiles = [];
  List<XFile> _selectedFeedbackVideos = [];

  final TextEditingController passengerNameController = TextEditingController();
  final TextEditingController pnrNoController = TextEditingController();
  final TextEditingController mobileNoController = TextEditingController();
  final TextEditingController emailIdController = TextEditingController();
  final TextEditingController otpController = TextEditingController();
  final TextEditingController coachNoController = TextEditingController();
  final TextEditingController berthNoController = TextEditingController();
  final TextEditingController trainNameController = TextEditingController();
  final TextEditingController commentController = TextEditingController();
  final ImagePicker _imagePicker = ImagePicker();

  late final StreamController<bool> _loadingController;
  late final StreamController<bool> _pnrValidationController;
  late final StreamController<bool> _feedbackSubmissionController;

  List<String> _trainNumbers = [];
  List<String> feedbackItems = [];

  @override
  void initState() {
    super.initState();
    _isWidgetActive = true;
    _loadingController = StreamController<bool>.broadcast();
    _pnrValidationController = StreamController<bool>.broadcast();
    _feedbackSubmissionController = StreamController<bool>.broadcast();
    _initializeControllers();
    _fetchTrainNumbers();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setInitialTrainNoFromUserModel();
    });
    feedbackItems = getFilteredFeedbackItems(selectedCategory!);
    feedbackRatings = List<int?>.filled(feedbackItems.length, null);
    formattedDate = Provider.of<UserModel>(context, listen: false).selectedDate;
  }

  void _initializeControllers() {
    _loadingController.stream.listen((isLoading) {
      if (_isWidgetActive && mounted) {
        setState(() => loading = isLoading);
      }
    });

    _pnrValidationController.stream.listen((isValid) {
      if (_isWidgetActive && mounted) {
        setState(() => isPNRVerified = isValid);
      }
    });

    _feedbackSubmissionController.stream.listen((isSubmitting) {
      if (_isWidgetActive && mounted) {
        setState(() => isUploading = isSubmitting);
      }
    });
  }

  void safeSetState(VoidCallback fn) {
    if (_isWidgetActive && mounted) {
      setState(fn);
    }
  }

  void _setInitialTrainNoFromUserModel() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final initialTrainNo = userModel.trainNo;
    if (initialTrainNo.isNotEmpty) {
      _onTrainNumberChanged(initialTrainNo);
    }
  }

  @override
  void dispose() {
    _isWidgetActive = false;
    _loadingController.close();
    _pnrValidationController.close();
    _feedbackSubmissionController.close();
    passengerNameController.dispose();
    pnrNoController.dispose();
    mobileNoController.dispose();
    emailIdController.dispose();
    otpController.dispose();
    coachNoController.dispose();
    berthNoController.dispose();
    trainNameController.dispose();
    commentController.dispose();
    super.dispose();
  }

  BuildContext? getSafeContext() {
    if (!_isWidgetActive || !mounted) return null;
    return context;
  }

   void _handleDateSelected(DateTime date) {
    setState(() {
      selectedDate = date;
      formattedDate = DateFormat('yyyy-MM-dd').format(date);
    });
    Provider.of<UserModel>(context, listen: false).setSelectedDate(
        "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}");
    // If you want to re-fetch charting data on date change:
    fetchChartingTime();
  }


  Future<void> showSafeDialog(String message, Color color) async {
    final BuildContext? context = getSafeContext();
    if (context == null) return;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context != null && mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            backgroundColor: color,
            content: Text(
              message,
              style: const TextStyle(color: Colors.black),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  "OK",
                  style: TextStyle(color: Colors.black),
                ),
              ),
            ],
          ),
        );
      }
    });
  }

  List<String> getFilteredFeedbackItems(String category) {
    switch (category) {
      case 'AC':
        return [
          'Cleaning of toilets (including tollet floor, commode pan, wall panels, shelf, miror, wash basin. Disinfectionnand provision of deodorant etc',
          'Cleaning of Passenger Compartment (including cleaning or passenger aisle, Vestibule area,, Doorway area and doorway wash basin, spraying of air freshene and cleaning of dust bin)',
          'Collection of gargage from the coach compartments and clearance of dustbins.',
          'Spraying of Mosquito/Cockroach/Fly Repellent and Providing Glue Board Whenever required or on demand by passengers',
          'Behavior/Response of Janitors / Supervisor (Including hygiene & cleanliness of Janitor/'
        ];
      case 'NONAC':
        return [
          'Cleaning of toilets, Wash Basin and other fittings (including Disinfection and provision of deodorant etc.',
          'Complete Cleaning of Passenger Compartment (Including spraying of air freshener and cleaning of dust bin',
          'Behavior of Janitors / Supervisor (Including hygiene & cleanliness of'
        ];
      case 'TT':
        return [
          'Cleaning of toilets (including tollet floor, commode pan, wall panels, shelf, miror, wash basin. Disinfectionnand provision of deodorant etc',
          'Cleaning of Passenger Compartment (including cleaning or passenger aisle, Vestibule area,, Doorway area and doorway wash basin, spraying of air freshene and cleaning of dust bin)',
          'Collection of gargage from the coach compartments and clearance of dustbins.',
          'Spraying of Mosquito/Cockroach/Fly Repellent and Providing Glue Board Whenever required or on demand by passengers',
          'Behavior/Response of Janitors / Supervisor (Including hygiene & cleanliness of Janitor/',
          'Cleaning of toilets, Wash Basin and other fittings (including Disinfection and provision of deodorant etc.',
          'Complete Cleaning of Passenger Compartment (Including spraying of air freshener and cleaning of dust bin)',
          'Behavior of Janitors / Supervisor (Including hygiene & cleanliness of'
        ];
      default:
        return [];
    }
  }

  // Fetch Train Numbers
  Future<void> _fetchTrainNumbers() async {
    try {
      _loadingController.add(true);
      _trainNumbers = await TrainService.getTrainNumbers();
      _loadingController.add(false);
    } catch (e) {
      _loadingController.add(false);
    }
  }

  // Train Number Change Handler
  void _onTrainNumberChanged(String? trainNumber) async {
    if (trainNumber != null && !isPNRVerified) {
      try {
        _loadingController.add(true);
        _selectedTrainNumber = trainNumber;
        final trainDetails =
            await EditTrainServices.fetchTrainDetailsByTrainNo(trainNumber);
        if (trainDetails != null && mounted) {
          setState(() {
            startTime = trainDetails.startTime ?? 'NA';
            endTime = trainDetails.endTime ?? 'NA';
            trainData = trainDetails;
            frequency = trainDetails.frequency ?? [];
          });

          final trainName = await fetchTrainName(trainNumber);
          setState(() {
            trainNameController.text = trainName ?? '';
            loading = false;
          });

          Provider.of<UserModel>(context, listen: false)
              .setTrainNo(trainNumber);
          fetchChartingTime();
        }
      } catch (e) {
        if (mounted) {
          _showWarning(
            context,
            "Failed to fetch train name",
            const Color.fromARGB(255, 147, 208, 227),
          );
        }
      } finally {
        if (mounted) {
          _loadingController.add(false);
        }
      }
    }
  }

  // Fetch Train Name
  Future<String?> fetchTrainName(String trainNumber) async {
    try {
      return await TrainService.getTrainName(trainNumber);
    } catch (e) {
      _showWarning(
        context,
        "Failed to fetch train name",
        const Color.fromARGB(255, 147, 208, 227),
      );
      return null;
    }
  }

  // Date Selection Handler
  void _handleDateSelection(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2030),
    );
    if (pickedDate != null && pickedDate != selectedDate) {
      safeSetState(() {
        selectedDate = pickedDate;
        formattedDate = DateFormat('yyyy-MM-dd').format(selectedDate);
      });
    }
    Provider.of<UserModel>(context, listen: false).setSelectedDate(
        "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}");
  }

  // Warning Dialog
  Future<void> _showWarning(
      BuildContext context, String message, Color color) async {
    final safeContext = getSafeContext();
    if (safeContext == null) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (safeContext != null && mounted) {
        showDialog(
          context: safeContext,
          builder: (BuildContext context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            backgroundColor: color,
            content: Text(
              message,
              style: const TextStyle(color: Colors.black),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  "OK",
                  style: TextStyle(color: Colors.black),
                ),
              ),
            ],
          ),
        );
      }
    });
  }

  void fetchChartingTime() async {
    // Ensure we have required data before making the API call
    if (_selectedTrainNumber == null || _selectedTrainNumber!.isEmpty) {
      print("No train number selected");
      return;
    }
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    //print("Selected Date : $selectedDate");
    try {
      final chartResponse = await TrainService.fetchChartingTime(
          trainNumber: _selectedTrainNumber,
          date: DateFormat('yyyy-MM-dd').format(selectedDate),
          token: userToken);
      //print("Charting Response : $chartResponse");

      if (chartResponse != null && mounted) {
        setState(() {
          chartData = chartResponse;
          trainScheduledAt = chartResponse.startTime;
          refreshedTimes = chartResponse.refreshedTimes;
          expectedChartingTime = chartResponse.chartingTime;
          loadedAtEarliest = chartResponse.loadedAtEarliest;
          loadedAtRecent = chartResponse.loadedAtRecent;
          isRunningDay = chartResponse.isRunningDay;
        });

        // Show modal only if train is not running
        if (!chartResponse.isRunningDay) {
          _showTrainStatusModal(false);
        }
      }
    } catch (e) {
      print('Error fetching charting time: $e');
      // Don't show error to user for charting time as it's not critical
      // But log it for debugging
      if (mounted) {
        setState(() {
          // Reset chart data on error
          chartData = null;
          isRunningDay = true; // Assume running to not block user
        });
      }
    }
  }

  void _showTrainStatusModal(bool isRunning) {
    String dayOfWeek = DateFormat('EEEE').format(selectedDate);
    if (!isRunning) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.red,
            title: const Text(
              "Train Not Running",
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 16.0,
                  fontWeight: FontWeight.bold),
            ),
            content: Text(
              "Train *$_selectedTrainNumber* is *NOT* running on *$dayOfWeek*\nRunning Days: ${_formatRunningDays()}",
              style: const TextStyle(color: Colors.black, fontSize: 15.0),
            ),
            actions: <Widget>[
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade100,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'OK',
                  style: TextStyle(
                      fontSize: 14.0,
                      color: Colors.blue,
                      fontWeight: FontWeight.bold),
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                  // Optional: Add any additional logic you want to execute when the modal is closed
                },
              ),
            ],
          );
        },
      );
    }
  }

  String _formatRunningDays() {
    if (frequency.isEmpty) return 'N/A';

    final dayAbbreviations = frequency.map((dayIndex) {
      final dayName =
          DateFormat('EEEE').format(DateTime(2023, 1, 1 + dayIndex));
      return dayName.substring(0, 3);
    }).toList();

    if (dayAbbreviations.length == 7) return 'Daily';

    return dayAbbreviations.join(', ');
  }

  bool _validatepnrNumber(String pnr) {
    if (pnr.isEmpty) return false;
    return (pnr.length == 8 || pnr.length == 10) && int.tryParse(pnr) != null;
  }

  int _countCharacters(String text) {
    return text.length;
  }

  Future<void> _validatePNR() async {
    if (!_validatepnrNumber(pnrNoController.text)) {
      _showWarning(context, "Invalid PNR number.", Colors.red);
      return;
    }
    setState(() => loading = true);

    try {
      final pnrData = await PnrService.checkPNR(pnrNoController.text);

      if (!_isWidgetActive || !mounted) return;
      safeSetState(() {
        _selectedTrainNumber = pnrData?.trainNumber;

        try {
          selectedDate = DateTime.parse(pnrData!.dateOfJourney);
        } catch (e) {
          List<String> dateParts = pnrData!.dateOfJourney.split('-');
          if (dateParts.length == 3) {
            selectedDate = DateTime(
              int.parse(dateParts[2]), // Year
              int.parse(dateParts[1]), // Month
              int.parse(dateParts[0]), // Day
            );
          }
        }

        formattedDate = DateFormat('yyyy-MM-dd').format(selectedDate);

        if (pnrData.passengerCoaches.isNotEmpty) {
          coachNoController.text = pnrData.passengerCoaches[0];
        }

        if (pnrData.passengerBerths.isNotEmpty) {
          berthNoController.text = pnrData.passengerBerths[0].toString();
        } else {
          berthNoController.text = '';
        }

        if (pnrData.trainName.isNotEmpty) {
          _trainName = pnrData.trainName;
          trainNameController.text = pnrData.trainName;
        }

        isPNRVerified = true;
      });

      if ((_trainName == null || _trainName!.isEmpty) &&
          _selectedTrainNumber != null) {
        try {
          final trainName = await fetchTrainName(_selectedTrainNumber!);
          if (mounted) {
            safeSetState(() {
              _trainName = trainName;
              trainNameController.text = trainName ?? '';
            });
          }
        } catch (e) {
          if (mounted) {
            _showWarning(
              context,
              "Failed to fetch train name",
              const Color.fromARGB(255, 144, 139, 139),
            );
          }
        }
      }

      _showWarning(
        context,
        "PNR details fetched successfully.",
        Colors.green,
      );
    } catch (e) {
      print("Error validating PNR: $e");
      if (mounted) {
        _showWarning(
          context,
          "Failed to validate PNR Details\n\n Invalid PNR Number",
          const Color.fromARGB(255, 241, 71, 71),
        );
      }
    } finally {
      if (mounted) {
        setState(() => loading = false);
      }
    }
  }

  Future<Map<String, dynamic>> _submitFeedback(String userToken) async {
    try {
      _feedbackSubmissionController.add(true);
      String verificationStatus = isEmailVerified ? "yes" : "no";
      String taskStatusSH = taskStatus == 'pending' ? 'P' : 'C';

      List<String?> feedbackValues = List.generate(8, (i) {
        return i < feedbackRatings.length && feedbackRatings[i] != null
            ? feedbackRatings[i].toString()
            : null;
      });

      int? parsedBerthNo;
      if (berthNoController.text.isNotEmpty) {
        parsedBerthNo = int.tryParse(berthNoController.text);
        if (parsedBerthNo == null) {
          throw Exception('Berth number must be a valid number');
        }
      }

      String? commentText =
          commentController.text.isNotEmpty ? commentController.text : null;

      List<Uint8List> pnrImageData = [];
      for (int i = 0; i < _selectedPNRFiles.length; i++) {
        final file = _selectedPNRFiles[i];
        final imageData = await _getFileData(file);
        pnrImageData.add(imageData);
      }

      List<Uint8List> feedbackImageData = [];
      for (int i = 0; i < _selectedFeedbackFiles.length; i++) {
        final file = _selectedFeedbackFiles[i];
        final imageData = await _getFileData(file);
        feedbackImageData.add(imageData);
      }

      List<Uint8List> feedbackVideoData = [];
      for (int i = 0; i < _selectedFeedbackVideos.length; i++) {
        final file = _selectedFeedbackVideos[i];
        final videoData = await _getFileData(file);
        feedbackVideoData.add(videoData);
      }

      final response = await SubmitPassengerFeedback.submitFeedback(
        email: emailIdController.text,
        token: userToken,
        date: formattedDate,
        passengerName: passengerNameController.text,
        mobileNo: mobileNoController.text,
        pnrNo: pnrNoController.text,
        status: taskStatusSH,
        verificationStatus: verificationStatus,
        feedbackValue1: feedbackValues[0],
        feedbackValue2: feedbackValues[1],
        feedbackValue3: feedbackValues[2],
        feedbackValue4: feedbackValues[3],
        feedbackValue5: feedbackValues[4],
        feedbackValue6: feedbackValues[5],
        feedbackValue7: feedbackValues[6],
        feedbackValue8: feedbackValues[7],
        coach: coachNoController.text,
        berthNo: parsedBerthNo,
        trainNumber: _selectedTrainNumber!,
        pnrImages: pnrImageData,
        imageFiles: feedbackImageData,
        videoFiles: feedbackVideoData,
        comment: commentText,
        category: selectedCategory,
      );

      if (mounted) {
        safeSetState(() {
          currentTimeInSeconds = DateTime.now().millisecondsSinceEpoch;
        });
      }

      return response;
    } catch (e) {
      return {'success': false, 'message': e.toString()};
    } finally {
      if (mounted) {
        _feedbackSubmissionController.add(false);
      }
    }
  }

  void _handleSubmit() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    if (_validateFields()) {
      try {
        loader(context, "submitting Feedback");
        final Map<String, dynamic> response =
            await _submitFeedback(userModel.token);
        Navigator.of(context, rootNavigator: true).pop();

        if (response.containsKey('success') && response['success'] == true) {
          _showWarning(
            context,
            response['message'] ?? "Feedback submitted successfully!",
            Colors.green,
          );
          _resetForm();
        } else {
          _showWarning(
            context,
            response['message'] ?? "Failed to submit feedback",
            Colors.red,
          );
        }
      } catch (e) {
        if (Navigator.canPop(context)) {
          Navigator.of(context, rootNavigator: true).pop();
        }

        print('Error in feedback submission: $e');
        _showWarning(
          context,
          "An unexpected error occurred. Please try again.",
          Colors.red,
        );
      }
    }
  }

  // Helper function to get file data as Uint8List
  Future<Uint8List> _getFileData(XFile file) async {
    if (kIsWeb) {
      return await file.readAsBytes();
    } else {
      return await File(file.path).readAsBytes();
    }
  }

  bool _isVideo(XFile file) {
    final extension = file.name.split('.').last.toLowerCase();
    return ['mp4', 'mov', 'avi', 'mkv'].contains(extension);
  }

  Widget _buildPNRImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ElevatedButton.icon(
          onPressed: _pickTicketImage,
          icon: const Icon(Icons.upload_file, color: Colors.blue),
          label: const Text('Upload PNR Image'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            side: const BorderSide(color: Colors.black87, width: 0.5),
            minimumSize: const Size(double.infinity, 50),
          ),
        ),
        const SizedBox(height: 10.0),
        _selectedPNRFiles.isNotEmpty
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Selected Images:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 200,
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 4,
                        mainAxisSpacing: 4,
                      ),
                      itemCount: _selectedPNRFiles.length,
                      itemBuilder: (context, index) {
                        final file = _selectedPNRFiles[index];
                        return Stack(
                          alignment: Alignment.topRight,
                          children: [
                            Container(
                              width: 100,
                              height: 100,
                              margin: const EdgeInsets.only(right: 8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey),
                              ),
                              child: kIsWeb
                                  ? Image.network(file.path, fit: BoxFit.cover)
                                  : Image.file(File(file.path),
                                      fit: BoxFit.cover),
                            ),
                            IconButton(
                              icon: const Icon(Icons.cancel,
                                  color: Colors.red, size: 20),
                              onPressed: () {
                                safeSetState(() {
                                  _selectedPNRFiles.removeAt(index);
                                });
                              },
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              )
            : const Center(
                child: Text(
                  'No PNR images selected',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
              ),
      ],
    );
  }

  Future<void> _pickTicketImage() async {
    if (_selectedPNRFiles.length >= 3) {
      _showWarning(
        context,
        'You can only select up to 3 PNR images',
        Colors.red,
      );
      return;
    }
    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo),
              title: const Text('Gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
          ],
        ),
      ),
    );

    if (source != null) {
      try {
        if (source == ImageSource.camera) {
          final XFile? pickedFile = await _imagePicker.pickImage(
            source: source,
            maxWidth: 800,
            maxHeight: 800,
            imageQuality: 90,
          );

          if (pickedFile != null) {
            safeSetState(() {
              if (_selectedPNRFiles.length < 3) {
                _selectedPNRFiles.add(pickedFile);
              } else {
                _showWarning(
                  context,
                  'Maximum 3 PNR images allowed',
                  Colors.red,
                );
              }
            });
          }
        } else {
          final List<XFile>? pickedFiles = await _imagePicker.pickMultiImage(
            maxWidth: 800,
            maxHeight: 800,
            imageQuality: 90,
          );

          if (pickedFiles != null && pickedFiles.isNotEmpty) {
            safeSetState(() {
              int remainingSlots = 3 - _selectedPNRFiles.length;

              if (remainingSlots <= 0) {
                _showWarning(
                  context,
                  'Maximum 3 PNR images allowed',
                  Colors.red,
                );
              } else if (pickedFiles.length <= remainingSlots) {
                _selectedPNRFiles.addAll(pickedFiles);
              } else {
                _selectedPNRFiles.addAll(pickedFiles.take(remainingSlots));
                _showWarning(
                  context,
                  'Only added ${remainingSlots} images. Maximum limit of 3 reached.',
                  Colors.red,
                );
              }
            });
          }
        }
      } catch (e) {
        _showWarning(
          context,
          'Error picking media: ${e.toString()}',
          Colors.red,
        );
      }
    }
  }

  Widget _buildFeedbackMediaSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ElevatedButton.icon(
          onPressed: _pickFeedbackMedia,
          icon: const Icon(Icons.photo_camera, color: Colors.blue),
          label: const Text('Pick Images/Videos for Feedback'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            side: const BorderSide(color: Colors.black87, width: 0.5),
            minimumSize: const Size(double.infinity, 50),
          ),
        ),
        const SizedBox(height: 10),
        _selectedFeedbackFiles.isNotEmpty
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Selected Images:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 200,
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 4,
                        mainAxisSpacing: 4,
                      ),
                      itemCount: _selectedFeedbackFiles.length,
                      itemBuilder: (context, index) {
                        final file = _selectedFeedbackFiles[index];
                        return Stack(
                          alignment: Alignment.topRight,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: Colors.grey[200],
                              ),
                              child: Center(
                                child: kIsWeb
                                    ? Image.network(file.path,
                                        fit: BoxFit.cover)
                                    : Image.file(File(file.path),
                                        fit: BoxFit.cover),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.cancel,
                                  color: Colors.red, size: 20),
                              onPressed: () {
                                safeSetState(() {
                                  _selectedFeedbackFiles.removeAt(index);
                                });
                              },
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              )
            : const Center(
                child: Text(
                  'No feedback images selected',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
              ),
      ],
    );
  }

  Widget _buildFeedbackVideoSection() {
    if (_selectedFeedbackVideos.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Selected Videos:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: _selectedFeedbackVideos.length,
              itemBuilder: (context, index) {
                final file = _selectedFeedbackVideos[index];
                return Stack(
                  alignment: Alignment.topRight,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.grey[300],
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.video_file,
                                size: 40, color: Colors.blue),
                            const SizedBox(height: 5),
                            Text(
                              file.name,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    ),
                    IconButton(
                      icon:
                          const Icon(Icons.cancel, color: Colors.red, size: 20),
                      onPressed: () {
                        safeSetState(() {
                          _selectedFeedbackVideos.removeAt(index);
                        });
                      },
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      );
    } else {
      return const SizedBox.shrink();
    }
  }

  // Pick feedback image/video
  Future<void> _pickFeedbackMedia() async {
    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo),
              title: const Text('Gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
          ],
        ),
      ),
    );

    if (source != null) {
      try {
        final bool? isVideo = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Select Media Type'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera),
                  title: const Text('Image'),
                  onTap: () => Navigator.pop(context, false),
                ),
                ListTile(
                  leading: const Icon(Icons.videocam),
                  title: const Text('Video'),
                  onTap: () => Navigator.pop(context, true),
                ),
              ],
            ),
          ),
        );

        if (isVideo == true) {
          // Check if already at video limit
          if (_selectedFeedbackVideos.length >= 3) {
            _showWarning(
              context,
              'You can only select up to 3 videos',
              Colors.red,
            );
            return;
          }

          final XFile? file = await _imagePicker.pickVideo(
            source: source,
            maxDuration: const Duration(minutes: 5),
          );

          if (file != null) {
            safeSetState(() {
              if (_selectedFeedbackVideos.length < 3) {
                _selectedFeedbackVideos.add(file);
              } else {
                _showWarning(
                  context,
                  'Maximum 3 videos allowed',
                  Colors.red,
                );
              }
            });
          }
        } else {
          // Check if already at image limit
          if (_selectedFeedbackFiles.length >= 3) {
            _showWarning(
              context,
              'You can only select up to 3 feedback images',
              Colors.red,
            );
            return;
          }

          if (source == ImageSource.camera) {
            // For camera, pick a single image
            final XFile? pickedFile = await _imagePicker.pickImage(
              source: source,
              maxWidth: 800,
              maxHeight: 800,
              imageQuality: 90,
            );

            if (pickedFile != null) {
              safeSetState(() {
                if (_selectedFeedbackFiles.length < 3) {
                  _selectedFeedbackFiles.add(pickedFile);
                } else {
                  _showWarning(
                    context,
                    'Maximum 3 feedback images allowed',
                    Colors.red,
                  );
                }
              });
            }
          } else {
            // For gallery, allow multiple selection with limit
            final List<XFile>? pickedFiles = await _imagePicker.pickMultiImage(
              maxWidth: 800,
              maxHeight: 800,
              imageQuality: 90,
            );

            if (pickedFiles != null && pickedFiles.isNotEmpty) {
              safeSetState(() {
                // Calculate how many more images can be added
                int remainingSlots = 3 - _selectedFeedbackFiles.length;

                if (remainingSlots <= 0) {
                  _showWarning(
                    context,
                    'Maximum 3 feedback images allowed',
                    Colors.red,
                  );
                } else if (pickedFiles.length <= remainingSlots) {
                  // Add all selected images if within limit
                  _selectedFeedbackFiles.addAll(pickedFiles);
                } else {
                  // Add only up to the remaining slots
                  _selectedFeedbackFiles
                      .addAll(pickedFiles.take(remainingSlots));
                  _showWarning(
                    context,
                    'Only added ${remainingSlots} images. Maximum limit of 3 reached.',
                    Colors.red,
                  );
                }
              });
            }
          }
        }
      } catch (e) {
        _showWarning(
          context,
          'Error picking media: ${e.toString()}',
          Colors.red,
        );
      }
    }
  }

  bool _validateFields() {
    if (passengerNameController.text.isEmpty ||
        pnrNoController.text.isEmpty ||
        _selectedTrainNumber == null ||
        mobileNoController.text.isEmpty ||
        coachNoController.text.isEmpty ||
        berthNoController.text.isEmpty) {
      _showWarning(
        context,
        "Please fill all fields with valid information.",
        Colors.red,
      );
      return false;
    }
    if (!_validatepnrNumber(pnrNoController.text)) {
      _showWarning(
        context,
        "Pnr number should be 8 or 10 digits",
        Colors.red,
      );
      return false;
    }

    // Validate that berthNo is a valid integer
    if (berthNoController.text.isNotEmpty) {
      final parsedBerthNo = int.tryParse(berthNoController.text);
      if (parsedBerthNo == null) {
        _showWarning(
          context,
          "Berth number must be a valid number",
          Colors.red,
        );
        return false;
      }
    }
    if (_countCharacters(commentController.text) > 100) {
      _showWarning(
        context,
        "Feedback cannot exceed 100 characters",
        Colors.red,
      );
      return false;
    }
    final items = getFilteredFeedbackItems(selectedCategory ?? 'AC');
    bool hasRating = false;

    for (int i = 0; i < items.length; i++) {
      if (feedbackRatings[i] != null) {
        hasRating = true;
        break;
      }
    }

    if (!hasRating) {
      _showWarning(
        context,
        "Please provide rating for at least one feedback item",
        Colors.red,
      );
      return false;
    }

    return true;
  }

  void _resetForm() {
    safeSetState(() {
      // Reset all form fields and state
      passengerNameController.clear();
      pnrNoController.clear();
      mobileNoController.clear();
      emailIdController.clear();
      otpController.clear();
      coachNoController.clear();
      berthNoController.clear();
      trainNameController.clear();
      commentController.clear();
      _selectedTrainNumber = null;
      _trainName = null;
      selectedDate = DateTime.now();
      formattedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
      isEmailVerified = false;
      isPNRVerified = false;
      showOtpInput = false;
      taskStatus = 'pending';
      selectedCategory = 'AC';
      feedbackItems = getFilteredFeedbackItems('AC');
      feedbackRatings = List<int?>.filled(feedbackItems.length, null);
      _selectedPNRFiles = [];
      _selectedFeedbackFiles = [];
      _selectedFeedbackVideos = [];
    });
  }

  // Email Verification Methods
  void _handleVerifyEmail() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    if (emailIdController.text.isEmpty) {
      _showWarning(context, "Please enter a valid Email ID.", Colors.white);
      return;
    }
    loader(context, "sending otp");
    try {
      await VerifyPassengerEmailService.verifyEmail(
        emailIdController.text,
        userModel.token,
      );
      safeSetState(() => showOtpInput = true);
      if (Navigator.canPop(context)) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      _showWarning(
        context,
        "Email verification initiated. Please check both your inbox and spam folders..",
        Colors.blue,
      );
    } catch (e) {
      if (Navigator.canPop(context)) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      _showWarning(
        context,
        "Error: ${e.toString()}",
        Colors.red,
      );
    } finally {
      safeSetState(() => loading = false);
    }
  }

  void _handleVerifyOtp(String formattedDate) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    if (otpController.text.isEmpty) {
      _showWarning(context, "Please enter the OTP.", Colors.red);
      return;
    }
    loader(context, "verifying otp");
    try {
      await ConfirmPassengerEmailService.confirmEmail(
        token: userModel.token,
        date: formattedDate,
        email: emailIdController.text,
        otp: otpController.text,
      );
      if (mounted) {
        safeSetState(() {
          showOtpInput = false;
          isEmailVerified = true;
        });
      }
      if (Navigator.canPop(context)) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      _showWarning(
        context,
        "OTP verified successfully.",
        Colors.blue,
      );
    } catch (e) {
      if (Navigator.canPop(context)) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      if (mounted) {
        _showWarning(
          context,
          "Error: ${e.toString().isEmpty ? 'Something went wrong' : e.toString()}",
          Colors.red,
        );
      }
    } finally {
      safeSetState(() => loading = false);
    }
  }

  bool _validateRmFields() {
    if (passengerNameController.text.isEmpty ||
        pnrNoController.text.isEmpty ||
        _selectedTrainNumber == null ||
        mobileNoController.text.isEmpty ||
        coachNoController.text.isEmpty ||
        berthNoController.text.isEmpty) {
      _showWarning(
        context,
        "Please fill all fields with valid information.",
        Colors.red,
      );
      return false;
    }
    if (!_validatepnrNumber(pnrNoController.text)) {
      _showWarning(
        context,
        "Pnr number should be 8 or 10 digits",
        Colors.red,
      );
      return false;
    }

    // Validate that berthNo is a valid integer
    if (berthNoController.text.isNotEmpty) {
      final parsedBerthNo = int.tryParse(berthNoController.text);
      if (parsedBerthNo == null) {
        _showWarning(
          context,
          "Berth number must be a valid number",
          Colors.red,
        );
        return false;
      }
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final userModel = Provider.of<UserModel>(context, listen: false);

    return Scaffold(
      appBar: const CustomAppBar(title: "Passenger Feedback"),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: () async => _fetchTrainNumbers(),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Padding(
                  padding: EdgeInsets.only(top: 5),
                  child: Text(
                    "Passenger Feedback",
                    style:
                        TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 10),
                _buildFormFields(),
                const SizedBox(height: 10),
                buildFeedbackTable2(context),
                const SizedBox(height: 10),
                buildFeedbackTable(),
                const SizedBox(height: 20),
                _buildFeedbackMediaSection(),
                _buildFeedbackVideoSection(),
                const SizedBox(height: 10),
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 5.0, vertical: 5.0),
                    child: Stack(
                      children: [
                        TextField(
                          controller: commentController,
                          maxLines: 2,
                          style: const TextStyle(fontSize: 14),
                          decoration: InputDecoration(
                            hintText: 'Add your feedback here...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                              borderSide: BorderSide(color: Colors.grey[400]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                              borderSide: const BorderSide(
                                  color: Colors.blue, width: 2.0),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                              borderSide: BorderSide(color: Colors.grey[400]!),
                            ),
                            errorBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                              borderSide: const BorderSide(
                                  color: Colors.red, width: 1.0),
                            ),
                            focusedErrorBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                              borderSide: const BorderSide(
                                  color: Colors.red, width: 2.0),
                            ),
                            filled: true,
                            fillColor: Colors.grey[50],
                            contentPadding: const EdgeInsets.fromLTRB(
                                10.0, 0.0, 10.0, 20.0),
                            counterText: '',
                          ),
                          inputFormatters: [
                            FilteringTextInputFormatter.deny(
                                RegExp(r'[0-9]')), // Deny numeric input
                            LengthLimitingTextInputFormatter(100),
                          ],
                          onChanged: (value) {
                            safeSetState(() {});
                          },
                        ),
                        Positioned(
                          right: 8,
                          bottom: 4,
                          child: Text(
                            '${_countCharacters(commentController.text)}/100 characters', // Change to characters
                            style: TextStyle(
                              fontSize: 10,
                              color: _countCharacters(commentController.text) >
                                      100 // Change to character count
                                  ? Colors.red
                                  : Colors.grey[600],
                              fontWeight:
                                  _countCharacters(commentController.text) >
                                          100 // Change to character count
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                _buildEmailVerificationSection(),

                const SizedBox(height: 30),
                // Task Status Dropdown
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: "Task Status *",
                    border: OutlineInputBorder(),
                  ),
                  value: taskStatus,
                  items: ['pending', 'completed'].map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(status),
                    );
                  }).toList(),
                  onChanged: (value) => safeSetState(() => taskStatus = value!),
                ),

                const SizedBox(height: 20),

                // Submit Button
                ElevatedButton(
                  onPressed: () {
                    if (!isUploading) {
                      _handleSubmit();
                    } else {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content:
                              Text('Please wait until the upload is complete'),
                        ),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.black,
                    side: const BorderSide(color: Colors.black87, width: 0.5),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text("Submit Feedback"),
                ),

                const SizedBox(height: 20),
                if (_selectedTrainNumber != null &&
                    (taskStatus == 'pending' || taskStatus == 'completed'))
                  FeedbackTable(
                    date: formattedDate,
                    token: userModel.token,
                    trainNumber: _selectedTrainNumber!,
                    selectedCategory: selectedCategory!,
                    currentTimeInSeconds: currentTimeInSeconds,
                  ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Train Number Dropdown Widget
  Widget _buildTrainNumberDropdown() {
    return DropdownSearch<String>(
      items: _trainNumbers,
      onChanged: _onTrainNumberChanged,
      popupProps: PopupProps.menu(
        showSearchBox: true,
        searchFieldProps: const TextFieldProps(
          decoration: InputDecoration(
            hintText: 'Search train number',
            border: OutlineInputBorder(),
          ),
        ),
        itemBuilder: (context, item, isSelected) => ListTile(
          title: Text(
            item,
            style: TextStyle(
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          selected: isSelected,
        ),
      ),
      selectedItem: _selectedTrainNumber,
      dropdownDecoratorProps: const DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(
          labelText: "Train Number *",
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
        ),
      ),
    );
  }

  // Build Form Fields
  Widget _buildFormFields() {
    return Column(
      children: [
        // PNR Number with Verify Button
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: pnrNoController,
                keyboardType: TextInputType.number,
                maxLength: 10,
                decoration: const InputDecoration(
                  labelText: "PNR Number *",
                  border: OutlineInputBorder(),
                  counterText: '',
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(10)
                ],
                onChanged: (value) {
                  // Force a rebuild when the text changes to update button state
                  setState(() {});
                },
              ),
            ),
            const SizedBox(width: 8),
            SizedBox(
              width: 120,
              child: isPNRVerified
                  ? Container(
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.check, color: Colors.white),
                          SizedBox(width: 4),
                          Text(
                            'Verified',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    )
                  : ElevatedButton(
                      onPressed:
                          (loading || !_validatepnrNumber(pnrNoController.text))
                              ? null
                              : _validatePNR,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        side: const BorderSide(color: Colors.blue, width: 0.5),
                        minimumSize: const Size(double.infinity, 56),
                        disabledBackgroundColor: Colors.grey.shade300,
                      ),
                      child: loading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.black,
                                strokeWidth: 1.0,
                              ),
                            )
                          : const Text(
                              "Validate",
                              style: TextStyle(color: Colors.white),
                            ),
                    ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        _buildPNRImageSection(),
        // Train Number Dropdown
        const SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: _buildTrainNumberDropdown(),
            ),
            const SizedBox(width: 8),
            Expanded(
              flex: 3,
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Train Name',
                  border: OutlineInputBorder(),
                ),
                readOnly: true,
                controller: trainNameController,
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),

        // Date of Journey
        Padding(
          padding: const EdgeInsets.only(top: 10, bottom: 10),
          child: DateSelect(
            initialDate: selectedDate,
            onDateSelected: _handleDateSelected,
          ),
        ),

        // Passenger Name
        const SizedBox(height: 10),
        TextFormField(
          controller: passengerNameController,
          decoration: const InputDecoration(
            labelText: "Passenger Name *",
            border: OutlineInputBorder(),
          ),
        ),

        const SizedBox(height: 10),

        // Coach and Berth Number Row
        Row(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: TextFormField(
                  controller: coachNoController,
                  decoration: const InputDecoration(
                    labelText: "Coach No *",
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: TextFormField(
                  controller: berthNoController,
                  decoration: const InputDecoration(
                    labelText: "Berth No *",
                    border: OutlineInputBorder(),
                  ),
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  keyboardType: TextInputType.number,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),

        // Mobile Number
        TextFormField(
          controller: mobileNoController,
          keyboardType: TextInputType.phone,
          maxLength: 10,
          decoration: const InputDecoration(
            labelText: "Mobile Number *",
            border: OutlineInputBorder(),
            counterText: '',
          ),
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        ),
      ],
    );
  }

  // Email Verification Section
  Widget _buildEmailVerificationSection() {
    return Column(
      children: [
        Container(
          width: double.infinity, // This makes the container take full width
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.all(4),
          child: const Text(
            "Email Verification (Optional)",
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
        const SizedBox(height: 10.0),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: emailIdController,
                decoration: const InputDecoration(
                  labelText: "Email ID",
                  border: OutlineInputBorder(),
                ),
                readOnly: isEmailVerified || showOtpInput,
              ),
            ),
            if (!isEmailVerified && !showOtpInput)
              IconButton(
                icon: const Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                ),
                onPressed: () {
                  _showSpamInfoDialog();
                },
              ),
          ],
        ),
        const SizedBox(height: 10),
        if (!isEmailVerified && !showOtpInput)
          ElevatedButton(
            onPressed: _handleVerifyEmail,
            style: _buttonStyle(),
            child: const Row(
              //mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.email),
                SizedBox(width: 8),
                Text("Verify Email"),
              ],
            ),
          ),
        if (showOtpInput) ...[
          const SizedBox(height: 10),
          // Notice about checking spam folder
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.mark_email_unread, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Flexible(
                  flex: 1,
                  child: Text(
                    "OTP sent to your email. Please check both inbox and spam folders.",
                    style: TextStyle(color: Colors.blue.shade700),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          TextFormField(
            controller: otpController,
            decoration: const InputDecoration(
              labelText: "Enter OTP",
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: () => _handleVerifyOtp(formattedDate),
            style: _buttonStyle(),
            child: const Text("Verify OTP"),
          ),
        ],
        if (isEmailVerified)
          const Row(
            children: [
              Icon(Icons.verified, color: Colors.green),
              SizedBox(width: 5.0),
              Text(
                "verified",
                style: TextStyle(color: Colors.green),
              ),
            ],
          ),
      ],
    );
  }

  void _showSpamInfoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.amber),
              SizedBox(width: 5),
              Text(
                "Email Verification Info",
                style: TextStyle(fontSize: 18, color: Colors.red),
              ),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Please note that verification emails may sometimes be delivered to your spam/junk folder.",
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 12),
              Text(
                "After requesting OTP:",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 4),
              Text("• Check your inbox first"),
              Text("• If not found, check spam/junk folder"),
              Text("• Add our domain to your safe sender list"),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text("I Understand"),
            ),
          ],
        );
      },
    );
  }

  // Consistent Button Style
  ButtonStyle _buttonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      side: const BorderSide(color: Colors.black87, width: 0.5),
    );
  }

  void _openFeedback(BuildContext context, String category) {
    safeSetState(() {
      selectedCategory = category;
      feedbackItems = getFilteredFeedbackItems(category);
      feedbackRatings = List<int?>.filled(feedbackItems.length, null);
    });
  }

  Widget buildFeedbackTable2(BuildContext context) {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ElevatedButton(
            onPressed: () => _openFeedback(context, 'AC'),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  selectedCategory == 'AC' ? Colors.blue : Colors.grey[300],
              foregroundColor:
                  selectedCategory == 'AC' ? Colors.white : Colors.black,
            ),
            child: const Text('AC'),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () => _openFeedback(context, 'NONAC'),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  selectedCategory == 'NONAC' ? Colors.blue : Colors.grey[300],
              foregroundColor:
                  selectedCategory == 'NONAC' ? Colors.white : Colors.black,
            ),
            child: const Text('NONAC'),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () => _openFeedback(context, 'TT'),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  selectedCategory == 'TT' ? Colors.blue : Colors.grey[300],
              foregroundColor:
                  selectedCategory == 'TT' ? Colors.white : Colors.black,
            ),
            child: const Text('TT'),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: () {
              if (_validateRmFields()) {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => RMPassengerFeedbackScreen(
                              date: formattedDate,
                              token:
                                  Provider.of<UserModel>(context, listen: false)
                                      .token,
                              trainNumber: _selectedTrainNumber ?? '',
                              trainName: trainNameController.text,
                              currentTimeInSeconds: currentTimeInSeconds,
                              pnrNumber: pnrNoController.text,
                              passengerName: passengerNameController.text,
                              coachNumber: coachNoController.text,
                              berthNumber: berthNoController.text,
                              mobileNumber: mobileNoController.text,
                              isPNRVerified: isPNRVerified,
                              selectedCategory: selectedCategory,
                            )));
              } else {
                _showWarning(
                  context,
                  "Please fill all required fields before proceeding to RM feedback.",
                  Colors.red,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  selectedCategory == 'RM' ? Colors.blue : Colors.grey[300],
              foregroundColor:
                  selectedCategory == 'RM' ? Colors.white : Colors.black,
            ),
            child: const Text('RM'),
          ),
        ],
      ),
    );
  }

  Widget buildFeedbackTable() {
    // print('Building feedback table');
    // print('Feedback items length: ${feedbackItems.length}');
    // print('Feedback ratings length: ${feedbackRatings.length}');

    final List<Map<String, dynamic>> ratings = [
      {'label': 'Excellent', 'value': 5},
      {'label': 'Very Good', 'value': 4},
      {'label': 'Good', 'value': 3},
      {'label': 'Average', 'value': 2},
      {'label': 'Poor', 'value': 1},
    ];

    final Map<int, Color> ratingColors = {
      5: Colors.green,
      4: Colors.yellow,
      3: Colors.red,
      2: Colors.red.shade900,
      1: Colors.red.shade800,
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Please tick (✓) in appropriate column",
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 10),
        Table(
          border: TableBorder.all(),
          columnWidths: const {
            0: FlexColumnWidth(2.3),
            1: FlexColumnWidth(0.5),
            2: FlexColumnWidth(0.5),
            3: FlexColumnWidth(0.5),
            4: FlexColumnWidth(0.5),
            5: FlexColumnWidth(0.5),
            6: FlexColumnWidth(0.5),
            7: FlexColumnWidth(0.5),
          },
          children: [
            TableRow(
              children: [
                const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text(
                    "ITEM",
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                ),
                ...ratings.map((rating) => Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: RotatedBox(
                        quarterTurns: 3,
                        child: Text(
                          rating['label'],
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 13),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    )),
              ],
            ),
            ...List.generate(feedbackItems.length, (index) {
              // print('Generating row for index: $index');
              return TableRow(
                children: [
                  Container(
                    height: 100,
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.vertical,
                      child: Text(
                        feedbackItems[index],
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ),
                  ...ratings.map((rating) {
                    bool isSelected = feedbackRatings[index] == rating['value'];
                    return GestureDetector(
                      onTap: () {
                        safeSetState(() {
                          // If the current rating is already selected, deselect it (set to null)
                          // Otherwise, set the new rating
                          feedbackRatings[index] =
                              feedbackRatings[index] == rating['value']
                                  ? null
                                  : rating['value'];
                        });
                      },
                      child: Container(
                        alignment: Alignment.center,
                        color: isSelected
                            ? ratingColors[rating['value']]
                            : Colors.white,
                        height: 110,
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Colors.black,
                              )
                            : null,
                      ),
                    );
                  }).toList(),
                ],
              );
            })
          ],
        ),
      ],
    );
  }
}
