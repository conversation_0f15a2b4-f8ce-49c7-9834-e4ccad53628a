import 'package:flutter/material.dart';
import 'package:railops/types/train_types/train_details_response.dart';
import 'package:railops/types/profile_types/add_trains_response.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:railops/widgets/bottom_modal_widget.dart';
import 'package:railops/widgets/call_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';

class AssignEhkCaTable extends StatefulWidget {
  final List<String> coaches;
  final List<String> caUsers;
  final List<String> ehkUsers;
  final Map<String, dynamic> coachWiseDict;
  final Map<String, dynamic> ehkDict;
  final String date;
  final String train;
  final bool down;
  final bool hasAccess;
  final bool updateBothTablesVisible;
  final Map<String, List<String>> selectedUsers;
  final List<String> selectedEhk;
  final Function(
      Map<String, dynamic>, Map<String, List<String>>, String, String) onSubmit;
  final Function(String) fetchLastJourneyDate;
  // final Function(String, List<String>, String, String)removeTrainDetailsOriginDate;
  final Function(String, List<String>,Map<String, String>) handleUpchange;
  final Function() clearSeletection;
  final Function(Map<String, List<String>>) onPendingDeletionsChanged;

  void clearPendingDeletions() {
    // Find the current state using GlobalKey
    final state = key as GlobalKey<State<AssignEhkCaTable>>;
    (state.currentState as dynamic)?.clearTablePendingDeletions();
  }

  final String status;

  const AssignEhkCaTable(
      {required this.coaches,
      required this.caUsers,
      required this.ehkUsers,
      required this.coachWiseDict,
      required this.ehkDict,
      required this.date,
      required this.train,
      required this.down,
      required this.hasAccess,
      required this.updateBothTablesVisible,
      required this.onSubmit,
      required this.fetchLastJourneyDate,
      // required this.removeTrainDetailsOriginDate,
      required this.handleUpchange,
      Key? key,
      required this.selectedUsers,
      required this.selectedEhk,
      required void Function(dynamic value) setSelectedUsers,
      required void Function(dynamic value) setSelectedEhk,
      required this.clearSeletection,
      required this.onPendingDeletionsChanged,
      required this.status})
      : super(key: key);

  @override
  AssignEhkCaTableState createState() => AssignEhkCaTableState();
}

class AssignEhkCaTableState extends State<AssignEhkCaTable> {
  Map<String, List<String>> selectedUsers = {};
  List<String> selectedEhk = [];
  Map<String, String> lastJourneyDetails = {};
  bool showMsgPopup = false;
  String message = '';
  String modalType = '';
  Map<String, List<String>> pendingDeletions = {};
  Map<String, String> _amountInHand = {};
  Map<String, String> get amountInHand => _amountInHand;

  bool get showCol {
    // Check if any coach has a value
    bool coachesHaveValue = widget.coaches
        .every((coach) => widget.coachWiseDict.containsKey(coach));
    return !coachesHaveValue;
  }

  void clearAmount() {
    _amountInHand.clear();
  }

  void clearTablePendingDeletions() {
    setState(() {
      pendingDeletions = {};
      widget.onPendingDeletionsChanged(pendingDeletions);
    });
  }

  void _markForDeletion(
      String username, String forUserType, List<String> coaches) {
        print("marking for deletion: $selectedUsers");;
    setState(() {
      final key = '$username-$forUserType';
      if (!pendingDeletions.containsKey(key)) {
        pendingDeletions[key] = coaches;
      } else {
        final existingCoaches = pendingDeletions[key] ?? [];
        final updatedCoaches = {...existingCoaches, ...coaches}.toList();
        pendingDeletions[key] = updatedCoaches;
      }
      // selectedUsers.remove(username);
      widget.onPendingDeletionsChanged(pendingDeletions);
    });
  }

  void _unmarkForDeletion(
      String username, String forUserType, List<String> coaches) {
    setState(() {
      final key = '$username-$forUserType';
      if (pendingDeletions.containsKey(key)) {
        final existingCoaches = pendingDeletions[key] ?? [];
        final updatedCoaches =
            existingCoaches.where((c) => !coaches.contains(c)).toList();
        if (updatedCoaches.isEmpty) {
          pendingDeletions.remove(key);
        } else {
          pendingDeletions[key] = updatedCoaches;
        }
        for (var coach in coaches) {
          selectedUsers.remove(coach);
        }
        widget.onPendingDeletionsChanged(pendingDeletions);
      }
    });
  }

  bool _isMarkedForDeletion(
      String username, String forUserType, List<String> coaches) {
    final key = '$username-$forUserType';
    return pendingDeletions.containsKey(key) &&
        coaches
            .every((coach) => pendingDeletions[key]?.contains(coach) ?? false);
  }

  @override
  void initState() {
    super.initState();
    _initializeSelectedUsers();
  }

  @override
  void didUpdateWidget(AssignEhkCaTable oldWidget) {
    super.didUpdateWidget(oldWidget);
    _initializeSelectedUsers();
  }

  void _initializeSelectedUsers() {
    setState(() {
      _amountInHand={};
    });
    print("initailizing the users : CoachWise Dict is : ${widget.coachWiseDict}");
    setState(() {
      selectedUsers = widget.selectedUsers;

      // Initialize amount data for selected CA users
      for (var entry in selectedUsers.entries) {
        List<String> usernames = entry.value;
        for (var username in usernames) {
          _fetchLastJourneyByUserName(username);
          if (!_amountInHand.containsKey(username)) {
            _amountInHand[username] = "0";
          }
        }
      }

      // Initialize amount data for existing assigned users
      widget.coaches.forEach((coach) {
        if (widget.coachWiseDict.containsKey(coach)) {
          widget.coachWiseDict[coach]!.forEach((user) {
            final userId = user['user'];
            // if (user['origin_date'] == widget.date &&
            //     !_amountInHand.containsKey(userId)) {
            //       setState(() {
            //         _amountInHand[userId] = user['cash_carry'].toString() ?? "0";
            //       });
              
            // }

            if (user['origin_date'] == widget.date) {
                if(!_amountInHand.containsKey(userId)){
                  setState(() {
                    _amountInHand[userId] = user['cash_carry'].toString() ?? "0";
                  });
                }
                else{
                  setState(() {
                    _amountInHand[userId] = user['cash_carry'].toString() ?? "0";
                  });
                }
            }
          });
        }
      });

      // Initialize amount data for existing EHK users
      widget.ehkDict.entries.forEach((entry) {
          _amountInHand[entry.key] =
              entry.value['cash_carry'].toString();
      });
    });
    print("Cash Carry : $_amountInHand");
  }
  // String _extractNumber(String input) {
  //   if (input.contains('_')) {
  //     return input.split('_').last; // Extract the number after the '_'
  //   }
  //   return input; // Return the input if no '_' is found
  // }

  dynamic _extractNumber(String input) {
    final RegExp phoneRegex = RegExp(r'\d{10}');
    final matches = phoneRegex.allMatches(input);
    final phoneNumbers = matches.map((match) => match.group(0)!).toList();

    if (phoneNumbers.length == 1) {
      return phoneNumbers.first;
    } else {
      return phoneNumbers;
    }
  }

  Future<void> _fetchLastJourneyByUserName(String value) async {
    if (!lastJourneyDetails.containsKey(value)) {
      try {
        final result = await widget.fetchLastJourneyDate(value);
        setState(() {
          lastJourneyDetails[value] = result;
        });
      } catch (error) {
        print('Error fetching journey date for $value: $error');
      }
    }
  }

  Future<void> _handleSelectChangeEhk(List<String> values) async {
    for (var value in values) {
      _fetchLastJourneyByUserName(value);
    }

    setState(() {
      selectedEhk = values;
    });

    // if (!widget.down) {
    widget.handleUpchange('ehk-user', values,_amountInHand);
    // }
  }

  Future<void> _handleSelectChange(String coach, List<String> values) async {
    for (var value in values) {
      _fetchLastJourneyByUserName(value);
    }
    setState(() {
      selectedUsers[coach] = values;
    });

    // if (!widget.down) {
    widget.handleUpchange('ca-user-$coach', values,_amountInHand);
    // }
  }

  void _showAmountUpdateDialog(String userId, String currentAmount,coach) {
    final TextEditingController amountController = TextEditingController(
      text: currentAmount.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update Amount for $userId'),
        content: TextField(
          controller: amountController,
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          decoration: const InputDecoration(
            labelText: 'Amount in Hand (₹)',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              print("Amount updated : ${amountController.text}");
              setState(() {
                _amountInHand[userId] = amountController.text;
                for (var data in widget.coachWiseDict[coach] ?? []) {
                  if (data['user'] == userId) {
                    data['cash_carry'] = amountController.text;
                    break;
                  }
              }
              });
              widget.handleUpchange(
                    'ca-user-$coach', [userId],_amountInHand);
              print("Amount updated : ${_amountInHand[userId]}");
              Navigator.pop(context);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _handleSubmit() {
    final data = _transformData(selectedUsers);
    final Map<String, List<String>> dataEhk = {};

    selectedEhk.forEach((ehk) {
      dataEhk[ehk] = widget.coaches.toList();
    });
    widget.onSubmit(data, dataEhk, widget.train, widget.date);
    widget.clearSeletection();
  }

  void _launchPhoneDialer(String phoneNumber) async {
    final countryCode = '+91';
    final formattedNumber = '$countryCode$phoneNumber';
    final url = 'tel:$formattedNumber';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not open the dialer';
    }
  }

  void openColorinfoDialog(
      Map<String, dynamic> user, String username, String lastLoginDate) {
    String message;

    String lastLoginData =
        "Last logged out at: ${user["last_logout_date_time"] ?? "N/A"}\n"
        "Last login at: ${user["last_login_date_time"] ?? "N/A"}\n"
        "Last user Location fetched at: ${user["last_user_location"] ?? "N/A"}\n"
        "Last location fetched on :${user["last_user_location_time"] ?? "N/A"} \n"
        "Last Location Fetched for train: ${user["last_location_fetched_for_train"] ?? "N/A"}";

    if (!user['isEmail']) {
      message = "Has registered with only phone number";
    } else if (user['isEmail'] && user['location_fetched_within_hr']) {
      message = "Has email id and phone number both\nlocation fetched an hour.";
    } else if (user['isEmail'] && user['isLoginBeforeTrainDate']) {
      message =
          "Has email id and phone number both\nRecently logged in after getting allocated for this journey.";
    } else if (user['isEmail'] && user['isLoginBefore']) {
      message =
          "Ha email id and phone number both\nAnd logged in any time before journey.";
    } else {
      message = "Entered his email id and phone number both.";
    }
    final msg = 'User : $username\n$message\n$lastLoginData';
    bottomModalWidget(context, msg);
  }

  Color getUserColor(Map<String, dynamic> user, isMarkedForDeletion) {
    // print( user['location_fetched_within_hr']);
    if (isMarkedForDeletion) {
      return Colors.grey.shade300;
    }
    if (!user['isEmail']) {
      return Colors.red.shade100;
    } else if (user['isEmail'] && user['location_fetched_within_hr']) {
      return Colors.green.shade900;
    } else if (user['isEmail'] && user['isLoginBeforeTrainDate']) {
      return Colors.green;
    } else if (user['isEmail'] && user['isLoginBefore']) {
      return Colors.green.shade100;
    } else {
      return Colors.yellow.shade200;
    }
  }

  Map<String, dynamic> _transformData(Map<String, List<String>> data) {
    final userToCoaches = <String, dynamic>{};

    // Process selected users from the form
    data.forEach((coach, users) {
      for (var user in users) {
        if (!userToCoaches.containsKey(user)) {
          userToCoaches[user] = {
            "values": [],
            "cash_carry": _amountInHand[user],
          };
        }
        userToCoaches[user]["values"]!.add(coach);
      }
    });

    // Also include any existing users with updated amounts
    widget.coaches.forEach((coach) {
      if (widget.coachWiseDict.containsKey(coach)) {
        widget.coachWiseDict[coach]!.forEach((user) {
          final userId = user['user'];
          if (_amountInHand.containsKey(userId) &&
              user['origin_date'] == widget.date &&
              !_isMarkedForDeletion(userId, "CA", [coach])) {
            if (!userToCoaches.containsKey(userId)) {
              userToCoaches[userId] = {
                "values": [coach],
                "cash_carry": _amountInHand[userId]!,
              };
            } else if (!userToCoaches[userId]["values"].contains(coach)) {
              userToCoaches[userId]["values"].add(coach);
            }
          }
        });
      }
    });

    return userToCoaches;
  }

  @override
  Widget build(BuildContext context) {
    return widget.coaches.isNotEmpty
        ? SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding:
                  const EdgeInsets.all(8.0), // Adding margin around the content
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                        bottom: 8.0), // Adding margin below the text
                    child: Text(
                      'Train Number: ${widget.train} - ${widget.down ? "in" : "out"}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontSize: 16), // Decreasing font size slightly
                    ),
                  ),
                  widget.status == "completed"
                      ? Container(
                          padding: const EdgeInsets.all(8),
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            color: const Color.fromARGB(255, 245, 190, 187),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text(
                            "This train is marked as completed, cannot be updated",
                            style: TextStyle(
                              color: Color.fromARGB(255, 237, 39, 39),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
                  ConstrainedBox(
                    constraints: const BoxConstraints(
                      minWidth: 270, // Set the minimum width
                    ),
                    child: DataTable(
                      border: TableBorder.all(
                        color: Colors.grey,
                        width: 1.0,
                        style: BorderStyle.solid,
                      ),
                      columns: [
                        const DataColumn(label: Text('')),
                        if (widget.hasAccess && showCol)
                          const DataColumn(label: Text('EHK/CA')),
                        DataColumn(
                          label: Center(
                            child: Column(
                              children: [
                                Text('Assigned'),
                                Text(
                                  '${DateFormat('dd-MMM-yyyy').format(DateTime.parse(widget.date))}'
                                  ' (${DateFormat('EEEE').format(DateTime.parse(widget.date))})',
                                  style: TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                          ),
                        ),

                        DataColumn(label: Text('Amount')),
                      ],
                      rows: [
                        _buildEhkRow(),
                        ...widget.coaches
                            .map((coach) => _buildCoachRow(coach))
                            .toList(),
                      ],
                      headingRowColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                          return Colors
                              .blue.shade100; // Header background color
                        },
                      ),
                      dataRowColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                          return states.contains(MaterialState.selected)
                              ? Colors.blue.shade50
                              : Colors.white; // Row background color
                        },
                      ),
                      columnSpacing: 20.0,
                      horizontalMargin: 10.0,
                      dividerThickness: 1.0,
                    ),
                  ),
                ],
              ),
            ),
          )
        : const SizedBox();
  }

  DataRow _buildEhkRow() {
    bool hasRightCellValue = widget.ehkDict.isNotEmpty &&
        widget.ehkDict.entries!
            .any((entry) => entry.value['origin_date'] == widget.date);

    final ehkUsername = widget.ehkDict.entries.firstWhere(
      (entry) => entry.value['origin_date'] == widget.date,
      orElse: () => const MapEntry('', null),
    );

    if (ehkUsername.key.isNotEmpty) {
      hasRightCellValue = hasRightCellValue &&
          !_isMarkedForDeletion(ehkUsername!.key, "EHK", widget.coaches);
    }

    return DataRow(cells: [
      const DataCell(
          Text('EHK', style: TextStyle(fontSize: 11))), // Reduced font size
      if (widget.hasAccess && showCol)
        DataCell(SizedBox(
          width: 150,
          child: Row(
            children: [
              ConstrainedBox(
                constraints: const BoxConstraints(
                  maxWidth: 130,
                  minHeight: 40,
                ),
                child: DropdownSearch<String>(
                  enabled: false,
                  items: widget.ehkUsers.toList(),
                  onChanged: hasRightCellValue
                      ? null
                      : (value) {
                          if (value != null) _handleSelectChangeEhk([value]);
                        },
                  dropdownDecoratorProps: const DropDownDecoratorProps(
                    dropdownSearchDecoration: InputDecoration(
                      labelText: 'Select User',
                      labelStyle: TextStyle(
                        fontSize: 11,
                      ),
                      border: InputBorder.none,
                    ),
                    baseStyle: TextStyle(
                      fontSize: 11,
                    ),
                  ),
                  popupProps: PopupProps.menu(
                    showSearchBox: true,
                    constraints: const BoxConstraints(
                      maxHeight: 250,
                    ),
                    itemBuilder: (context, item, isSelected) {
                      return ListTile(
                        title: Text(item, style: const TextStyle(fontSize: 11)),
                        selected: isSelected,
                      );
                    },
                  ),
                  selectedItem: selectedEhk.isEmpty ? null : selectedEhk.first,
                ),
              ),
              ...selectedEhk.map((username) {
                print(" EHK_phone: ${_extractNumber(username)}");
                final lastJourney = lastJourneyDetails[username];
                return lastJourney != null
                    ? Row(
                        children: [
                          Tooltip(
                            message: lastJourney,
                            triggerMode: TooltipTriggerMode.tap,
                            child: GestureDetector(
                              onTap: () {
                                String extractedNumber =
                                    _extractNumber(username);
                                _launchPhoneDialer(extractedNumber);
                                // print('Icon clicked! $phoneNumber');
                              },
                              child: const Icon(Icons.info, size: 16),
                            ),
                          ),
                        ],
                      )
                    : const SizedBox.shrink();
              }).toList(),
            ],
          ),
        )),
      DataCell(
        widget.ehkDict.isNotEmpty
            ? Column(
                children: widget.ehkDict.entries
                    .where((entry) => entry.value['origin_date'] == widget.date)
                    .map<Widget>((entry) {
                  // print(" EHK_phone: ${_extractNumber(entry.key)}");
                  // print(" CA: ${entry.value}");
                  // final isEmail = entry.value['isEmail'] ?? false;
                  // final isLoginBeforeTrainDate = entry.value['isLoginBeforeTrainDate'] ?? false;
                  final isMarkedForDeletion =
                      _isMarkedForDeletion(entry.key, "EHK", widget.coaches);

                  return Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 0.0, horizontal: 12.0),
                    decoration: BoxDecoration(
                        color: getUserColor(entry.value, isMarkedForDeletion)),
                    child: Row(
                      children: [
                        IconButton(
                          onPressed: () {
                            showCallModal(context, entry.key.toString());
                          },
                          icon: const Icon(
                            Icons.call,
                            color: Colors.blue,
                          ),
                        ),

                        GestureDetector(
                          onLongPress: () {
                            showCallModal(context, entry.key.toString());
                          },
                          child: Text(
                            entry.key,
                            style: TextStyle(
                              fontSize: 11,
                              decoration: isMarkedForDeletion
                                  ? TextDecoration.lineThrough
                                  : TextDecoration.none,
                              color: isMarkedForDeletion
                                  ? Colors.grey
                                  : Colors.black,
                            ),
                          ),
                        ),
                        const Spacer(),
                        // if (widget.hasAccess)
                        //   IconButton(
                        //     icon: Icon(
                        //       isMarkedForDeletion
                        //           ? Icons.undo
                        //           : Icons.delete,
                        //       color: isMarkedForDeletion
                        //           ? Colors.blue
                        //           : Colors.red,
                        //     ),
                        //     onPressed: () {
                        //       if (isMarkedForDeletion) {
                        //         _unmarkForDeletion(entry.key, "EHK", widget.coaches);
                        //       } else {
                        //         _markForDeletion(entry.key, "EHK", widget.coaches);
                        //       }
                        //     },
                        //   ),
                        IconButton(
                            onPressed: () {
                              openColorinfoDialog(entry.value, entry.key,
                                  entry.value['last_login_date_time'] ?? 'N/A');
                            },
                            icon: const Icon(Icons.info))
                      ],
                    ),
                  );
                }).toList(),
              )
            : const SizedBox.shrink(),
      ),

      DataCell(
        widget.ehkDict.isNotEmpty
            ? Column(
                children: widget.ehkDict.entries
                    .where((entry) => entry.value['origin_date'] == widget.date)
                    .map<Widget>((entry) {
                  final isMarkedForDeletion =
                      _isMarkedForDeletion(entry.key, "EHK", widget.coaches);
                  final String userId = entry.key;
                  final String amount = _amountInHand[userId] ?? "0";

                  return Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12.0, horizontal: 12.0),
                    decoration: BoxDecoration(
                        color: getUserColor(entry.value, isMarkedForDeletion)),
                    child: Row(
                      children: [
                        Text(
                          '₹${amount}',
                          style: TextStyle(
                            fontSize: 11,
                            decoration: isMarkedForDeletion
                                ? TextDecoration.lineThrough
                                : TextDecoration.none,
                            color: isMarkedForDeletion
                                ? Colors.grey
                                : Colors.black,
                          ),
                        ),
                        const Spacer(),
                        // if (widget.hasAccess)
                        //   IconButton(
                        //     icon: const Icon(
                        //       Icons.edit,
                        //       color: Colors.blue,
                        //       size: 16,
                        //     ),
                        //     onPressed: () {
                        //       // _showAmountUpdateDialog(userId, amount);
                        //     },
                        //   ),
                      ],
                    ),
                  );
                }).toList(),
              )
            : const SizedBox.shrink(),
      ),
    ]);
  }

  DataRow _buildCoachRow(String coach) {
    // Determine if there is a value in the right DataCell based on the conditions provided
    bool hasRightCellValue = widget.coachWiseDict.containsKey(coach) &&
        widget.coachWiseDict[coach]!
            .any((user) => user['origin_date'] == widget.date);

    if (widget.coachWiseDict.containsKey(coach)) {
      final caUsername = widget.coachWiseDict[coach]!.firstWhere(
        (entry) => entry['origin_date'] == widget.date,
        orElse: () => const MapEntry('', null),
      );

      if (caUsername['user'].isNotEmpty) {
        hasRightCellValue = hasRightCellValue &&
            !_isMarkedForDeletion(caUsername['user'], "CA", [coach]);
      }
    }

    return DataRow(
      cells: [
        DataCell(Text(coach,
            style: const TextStyle(fontSize: 11))), // Reduced font size
        if (widget.hasAccess && showCol)
          DataCell(
            Row(
              children: [
                ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 140,
                    minHeight: 40,
                  ),
                  child: DropdownSearch<String>(
                    enabled: !hasRightCellValue,
                    items: widget.caUsers.toList(),
                    onChanged: hasRightCellValue
                      ? null
                      : (value) {
                          if (value != null) {
                            _handleSelectChange(coach, [value]);
                          }
                        },
                    dropdownDecoratorProps: const DropDownDecoratorProps(
                      dropdownSearchDecoration: InputDecoration(
                        labelText: 'Select User',
                        labelStyle: TextStyle(
                          fontSize: 11,
                        ),
                        border: InputBorder.none,
                      ),
                      baseStyle: TextStyle(
                        fontSize: 11,
                      ),
                    ),
                    popupProps: PopupProps.menu(
                      showSearchBox: true,
                      constraints: const BoxConstraints(
                        maxHeight: 250,
                      ),
                      itemBuilder: (context, item, isSelected) {
                        return ListTile(
                          title:
                              Text(item, style: const TextStyle(fontSize: 11)),
                          selected: isSelected,
                        );
                      },
                    ),
                    selectedItem: hasRightCellValue
                        ? null
                        : (selectedUsers[coach]?.isEmpty ?? true
                            ? null
                            : selectedUsers[coach]?.first),
                  ),
                ),
                ...(selectedUsers[coach] ?? []).map((username) {
                  final lastJourney = lastJourneyDetails[username];
                  return lastJourney != null
                      ? Row(
                          children: [
                            Tooltip(
                              message: lastJourney,
                              triggerMode: TooltipTriggerMode.tap,
                              child: GestureDetector(
                                onTap: () {
                                  String extractedNumber =
                                      _extractNumber(username);
                                  _launchPhoneDialer(extractedNumber);
                                },
                                child: const Icon(Icons.info, size: 16),
                              ),
                            ),
                          ],
                        )
                      : const SizedBox.shrink();
                }).toList(),
              ],
            ),
          ),
        DataCell(
          widget.coachWiseDict.containsKey(coach)
              ? Column(
                  children: widget.coachWiseDict[coach]!
                      .where((user) => user['origin_date'] == widget.date)
                      .map<Widget>((user) {
                    final isMarkedForDeletion =
                        _isMarkedForDeletion(user['user'], "CA", [coach]);

                    return Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 0.0, horizontal: 10.0),
                      decoration: BoxDecoration(
                          color: getUserColor(user, isMarkedForDeletion)),
                      child: Row(
                        children: [
                          IconButton(
                            onPressed: () {
                              showCallModal(context, user['user'].toString());
                            },
                            icon: const Icon(
                              Icons.call,
                              color: Colors.blueAccent,
                            ),
                          ),
                          GestureDetector(
                            onLongPress: () {
                              showCallModal(context, user['user'].toString());
                            },
                            child: Text(
                              "${user['user'].split('_')[0]}_${user['user'].split('_')[1]}",
                              style: TextStyle(
                                fontSize: 11,
                                decoration: isMarkedForDeletion
                                    ? TextDecoration.lineThrough
                                    : TextDecoration.none,
                                color: isMarkedForDeletion
                                    ? Colors.grey
                                    : Colors.black,
                              ),
                            ),
                          ),
                          const Spacer(),
                          if (widget.hasAccess)
                            Align(
                              alignment: Alignment.centerRight,
                              child: IconButton(
                                icon: Icon(
                                  isMarkedForDeletion
                                      ? Icons.undo
                                      : Icons.delete,
                                  color: isMarkedForDeletion
                                      ? Colors.blue
                                      : Colors.red,
                                ),
                                onPressed: () {
                                  if (isMarkedForDeletion) {
                                    _unmarkForDeletion(
                                        user['user'], "CA", [coach]);
                                  } else {
                                    _markForDeletion(
                                        user['user'], "CA", [coach]);
                                  }
                                },
                              ),
                            ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: IconButton(
                                onPressed: () {
                                  openColorinfoDialog(user, user['user'],
                                      user['last_login_date_time'] ?? 'N/A');
                                },
                                icon: const Icon(Icons.info)),
                          )
                        ],
                      ),
                    );
                  }).toList(),
                )
              : const SizedBox.shrink(),
        ),
        DataCell(
          widget.coachWiseDict.containsKey(coach)
              ? Column(
                  children: widget.coachWiseDict[coach]!
                      .where((user) => user['origin_date'] == widget.date)
                      .map<Widget>((user) {
                    final userId = user['user'];
                    final isMarkedForDeletion =
                        _isMarkedForDeletion(userId, "CA", [coach]);
                    // final String amount = _amountInHand[userId] ?? "0";
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 0.0, horizontal: 10.0),
                      decoration: BoxDecoration(
                          color: getUserColor(user, isMarkedForDeletion)),
                      child: Row(
                        children: [
                          Text(
                            '₹${_amountInHand[userId] ?? "0"}',
                            key: ValueKey(amountInHand[userId]),
                            style: TextStyle(
                              fontSize: 11,
                              decoration: isMarkedForDeletion
                                  ? TextDecoration.lineThrough
                                  : TextDecoration.none,
                              color: isMarkedForDeletion
                                  ? Colors.grey
                                  : Colors.black,
                            ),
                          ),
                          const Spacer(),
                          if (widget.hasAccess)
                            IconButton(
                              icon: const Icon(
                                Icons.edit,
                                color: Colors.blue,
                                size: 16,
                              ),
                              onPressed: () {
                                _showAmountUpdateDialog(userId, _amountInHand[userId] ?? "0",coach);
                              },
                            ),
                        ],
                      ),
                    );
                  }).toList(),
                )
              : const SizedBox.shrink(),
        )
      ],
    );
  }
}