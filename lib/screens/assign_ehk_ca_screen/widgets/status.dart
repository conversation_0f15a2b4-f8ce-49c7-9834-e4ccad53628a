import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/assign_ehk_ca_services/job_chart_status_services.dart';
import 'package:railops/types/assign_ehk_ca_types/job_chart_status_types.dart';

class JobChartStatusWidget extends StatefulWidget {
  final String trainNumber;
  final String originDate;
  final String statusFor;
  final String defaultStatus;
  final Function(String, String, String, String, String)submitJobChartStatus;

  JobChartStatusWidget({
    required this.trainNumber,
    required this.originDate,
    required this.statusFor,
    this.defaultStatus = 'pending',
    required this.submitJobChartStatus,
  });

  @override
  _JobChartStatusWidgetState createState() => _JobChartStatusWidgetState();
}

class _JobChartStatusWidgetState extends State<JobChartStatusWidget> {
  late String _selectedStatus;
  bool _isLoading = false;
  late String token;

  @override
  void initState() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    token = userModel.token;
    _selectedStatus = widget.defaultStatus;
    super.initState();
  }


  @override
  void didUpdateWidget(covariant JobChartStatusWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget. defaultStatus!= oldWidget.defaultStatus) {
      _selectedStatus = widget.defaultStatus;
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isCompleted = widget.defaultStatus == 'completed';
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey, width: 1.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedStatus,
                onChanged: isCompleted
                    ? null
                    : (String? newValue) {
                        setState(() => _selectedStatus = newValue!);
                      },
                items: ['pending', 'completed']
                    .map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
            ),
          ),
          // SizedBox(width: 20),
          // _isLoading
          //     ? CircularProgressIndicator()
          //     : Align(
          //         alignment: Alignment.center,
          //         child: ElevatedButton(
          //           onPressed: isCompleted ? null : () async {
          //             try {
          //               await widget.submitJobChartStatus(widget.trainNumber,widget.originDate,widget.statusFor,_selectedStatus, token);
          //             } catch (e) {
          //               print("Error submitting status: $e");
          //             }
          //           },
          //           style: ElevatedButton.styleFrom(
          //             padding: EdgeInsets.all(16),
          //             foregroundColor: Colors.black87,
          //             backgroundColor: isCompleted ? Colors.grey : Colors.white,
          //             side: BorderSide(color: Colors.black87, width: 0.5),
          //           ),
          //           child: const Text(
          //             'Update Status',
          //             style: TextStyle(
          //               fontSize: 16.0,
          //             ),
          //           ),
          //         ),
          //       ),
        ],
      ),
    );
  }
}