import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/firestore_token_service.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/services/authentication_services/jwt_service.dart';

/// Comprehensive testing service for the notification system
/// This service helps test the complete end-to-end notification flow
class NotificationTestService {
  /// Test FCM token generation and storage
  static Future<Map<String, dynamic>> testFcmTokenGeneration() async {
    final result = <String, dynamic>{
      'test_name': 'FCM Token Generation',
      'timestamp': DateTime.now().toIso8601String(),
      'success': false,
      'details': {},
      'errors': [],
    };

    try {
      print('🧪 Testing FCM Token Generation...');

      // Test getting fresh FCM token
      final fcmToken = await FcmTokenService.getFreshFcmToken();
      result['details']['fcm_token_available'] = fcmToken != null;
      result['details']['fcm_token_length'] = fcmToken?.length ?? 0;
      result['details']['fcm_token_valid'] =
          FcmTokenService.isValidFcmToken(fcmToken);

      if (fcmToken != null) {
        print('✅ FCM Token generated successfully');
        print('📱 Token length: ${fcmToken.length}');
        print(
            '✅ Token validation: ${FcmTokenService.isValidFcmToken(fcmToken)}');

        // Test token storage in SharedPreferences
        final storedToken = await FcmTokenService.getFcmToken();
        result['details']['token_stored_locally'] = storedToken == fcmToken;

        if (storedToken == fcmToken) {
          print('✅ Token stored in SharedPreferences successfully');
        } else {
          print('❌ Token storage in SharedPreferences failed');
          result['errors'].add('Token not stored in SharedPreferences');
        }

        result['success'] = true;
      } else {
        print('❌ FCM Token generation failed');
        result['errors'].add('FCM token generation returned null');
      }
    } catch (e) {
      print('❌ FCM Token test error: $e');
      result['errors'].add('Exception: $e');
    }

    return result;
  }

  /// Test Firestore token storage and retrieval
  static Future<Map<String, dynamic>> testFirestoreTokenStorage() async {
    final result = <String, dynamic>{
      'test_name': 'Firestore Token Storage',
      'timestamp': DateTime.now().toIso8601String(),
      'success': false,
      'details': {},
      'errors': [],
    };

    try {
      print('🧪 Testing Firestore Token Storage...');

      // Test Firestore connection
      final connectionTest =
          await FirestoreTokenService.testFirestoreConnection();
      result['details']['firestore_connection'] = connectionTest;

      if (!connectionTest) {
        print('❌ Firestore connection test failed');
        result['errors'].add('Firestore connection failed');
        return result;
      }

      print('✅ Firestore connection successful');

      // Test user ID retrieval
      final userId = await _getUserId();
      result['details']['user_id_available'] = userId != null;
      result['details']['user_id'] = userId;

      if (userId == null) {
        print('❌ No user ID available');
        result['errors'].add('User ID not found in SharedPreferences');
        return result;
      }

      print('✅ User ID found: $userId');

      // Test token storage in Firestore
      final storeResult = await FirestoreTokenService.storeTokenInFirestore();
      result['details']['token_stored_firestore'] = storeResult;

      if (storeResult) {
        print('✅ Token stored in Firestore successfully');

        // Test token retrieval from Firestore
        final retrievedToken =
            await FirestoreTokenService.getTokenFromFirestore(userId);
        result['details']['token_retrieved_firestore'] = retrievedToken != null;

        if (retrievedToken != null) {
          print('✅ Token retrieved from Firestore successfully');
          print('📱 Retrieved token length: ${retrievedToken.length}');

          // Test token info
          final tokenInfo = await FirestoreTokenService.getTokenInfo();
          result['details']['token_info'] = tokenInfo;

          result['success'] = true;
        } else {
          print('❌ Token retrieval from Firestore failed');
          result['errors'].add('Token not found in Firestore after storage');
        }
      } else {
        print('❌ Token storage in Firestore failed');
        result['errors'].add('Failed to store token in Firestore');
      }
    } catch (e) {
      print('❌ Firestore test error: $e');
      result['errors'].add('Exception: $e');
    }

    return result;
  }

  /// Test Cloud Function endpoint
  static Future<Map<String, dynamic>> testCloudFunctionEndpoint() async {
    final result = <String, dynamic>{
      'test_name': 'Cloud Function Endpoint',
      'timestamp': DateTime.now().toIso8601String(),
      'success': false,
      'details': {},
      'errors': [],
    };

    try {
      print('🧪 Testing Cloud Function Endpoint...');

      // Get user ID
      final userId = await _getUserId();
      if (userId == null) {
        print('❌ No user ID available for Cloud Function test');
        result['errors'].add('User ID not found');
        return result;
      }

      // Test with New Delhi coordinates
      final testResult = await FirebaseCloudFunctionService.callNotifyFunction(
        userId: userId,
        trainNumber: '12345',
        date: FirebaseCloudFunctionService.getCurrentDateString(),
        lat: '28.6139', // New Delhi
        lng: '77.2090',
      );

      result['details']['cloud_function_response'] = testResult;
      result['details']['response_status'] = testResult['status'];
      result['details']['response_message'] = testResult['message'];

      print('📡 Cloud Function Response:');
      print('   Status: ${testResult['status']}');
      print('   Message: ${testResult['message']}');

      if (testResult['status'] == 'sent') {
        print('✅ Cloud Function test successful - Notification sent');
        result['success'] = true;
      } else if (testResult['status'] == 'skipped') {
        print(
            '⚠️ Cloud Function test - Notification skipped: ${testResult['message']}');
        result['success'] = true; // Skipped is also a valid response
      } else {
        print('❌ Cloud Function test failed: ${testResult['message']}');
        result['errors'].add('Cloud Function error: ${testResult['message']}');
      }
    } catch (e) {
      print('❌ Cloud Function test error: $e');
      result['errors'].add('Exception: $e');
    }

    return result;
  }

  /// Test complete notification sync
  static Future<Map<String, dynamic>> testCompleteNotificationSync() async {
    final result = <String, dynamic>{
      'test_name': 'Complete Notification Sync',
      'timestamp': DateTime.now().toIso8601String(),
      'success': false,
      'details': {},
      'errors': [],
    };

    try {
      print('🧪 Testing Complete Notification Sync...');

      // Get user token for API sync
      final prefs = await SharedPreferences.getInstance();
      final authToken = prefs.getString('authToken');

      if (authToken == null) {
        print('❌ No auth token available for sync test');
        result['errors'].add('Auth token not found');
        return result;
      }

      // Test comprehensive token sync
      final syncResult = await FcmTokenService.ensureTokenSynced(authToken);
      result['details']['sync_successful'] = syncResult;

      if (syncResult) {
        print('✅ Complete token sync successful');

        // Verify Firestore sync
        final firestoreSync = await FirestoreTokenService.syncWithFirestore();
        result['details']['firestore_sync'] = firestoreSync;

        if (firestoreSync) {
          print('✅ Firestore sync verified');
          result['success'] = true;
        } else {
          print('❌ Firestore sync verification failed');
          result['errors'].add('Firestore sync failed');
        }
      } else {
        print('❌ Complete token sync failed');
        result['errors'].add('Token sync failed');
      }
    } catch (e) {
      print('❌ Complete sync test error: $e');
      result['errors'].add('Exception: $e');
    }

    return result;
  }

  /// Run all notification tests
  static Future<Map<String, dynamic>> runAllTests() async {
    print('🚀 Starting Complete Notification System Tests...');
    print('=' * 60);

    final allResults = <String, dynamic>{
      'test_suite': 'Complete Notification System',
      'timestamp': DateTime.now().toIso8601String(),
      'overall_success': false,
      'tests': {},
      'summary': {},
    };

    // Run all tests
    final tests = [
      testFcmTokenGeneration,
      testFirestoreTokenStorage,
      testCompleteNotificationSync,
      testCloudFunctionEndpoint,
    ];

    int passedTests = 0;
    int totalTests = tests.length;

    for (final test in tests) {
      try {
        final testResult = await test();
        final testName = testResult['test_name'];
        allResults['tests'][testName] = testResult;

        if (testResult['success'] == true) {
          passedTests++;
          print('✅ $testName: PASSED');
        } else {
          print('❌ $testName: FAILED');
          if (testResult['errors'].isNotEmpty) {
            for (final error in testResult['errors']) {
              print('   Error: $error');
            }
          }
        }
        print('-' * 40);
      } catch (e) {
        print('❌ Test execution error: $e');
        print('-' * 40);
      }
    }

    allResults['summary'] = {
      'total_tests': totalTests,
      'passed_tests': passedTests,
      'failed_tests': totalTests - passedTests,
      'success_rate': (passedTests / totalTests * 100).toStringAsFixed(1),
    };

    allResults['overall_success'] = passedTests == totalTests;

    print('📊 Test Summary:');
    print('   Total Tests: $totalTests');
    print('   Passed: $passedTests');
    print('   Failed: ${totalTests - passedTests}');
    print('   Success Rate: ${allResults['summary']['success_rate']}%');
    print('=' * 60);

    return allResults;
  }

  /// Helper method to get user ID (updated to use JWT-extracted user ID)
  static Future<String?> _getUserId() async {
    try {
      // First try to get JWT-extracted user ID (most reliable)
      final jwtUserId = await JwtService.getStoredUserId();
      if (jwtUserId != null) {
        return jwtUserId;
      }

      final prefs = await SharedPreferences.getInstance();

      // Fallback: try to get user ID from login response
      final loginResponseJson = prefs.getString('loginResponse');
      if (loginResponseJson != null) {
        final loginData = json.decode(loginResponseJson);
        return loginData['user_id']?.toString() ??
            loginData['id']?.toString() ??
            loginData['userId']?.toString();
      }

      // Last fallback: try direct user_id key
      return prefs.getString('user_id');
    } catch (e) {
      debugPrint('Error getting user ID: $e');
      return null;
    }
  }
}
