import 'package:flutter/foundation.dart';
import 'package:railops/models/index.dart';
import 'package:railops/services/api_services/index.dart';
import 'package:railops/types/attendance_types/onboarding_response.dart';
import '../firebase_cloud_function_service.dart';
import '../fcm_token_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OnboardingService {
  String _token = '';

  OnboardingService() {
    _getToken();
  }

  Future<void> _getToken() async {
    final userModel = UserModel();
    await userModel.loadUserData();
    _token = userModel.token;
  }

  static Future<OnboardingResponse> fetchOnboardingDetails(
    final String trainNumber,
    final String date, {
    bool enableNotificationTrigger = true,
  }) async {
    try {
      final responseJson = await ApiService.get(
        // '/api/onboarding_details_by_nearby_stations/',
        "/microservice/train/location/?train_number=$trainNumber&date=$date",
        {
          'token': await _fetchToken(),
          if (trainNumber.isNotEmpty) 'train_number': trainNumber,
          if (date.isNotEmpty) 'date': date,
        },
      );

      final response = OnboardingResponse.fromJson(responseJson);

      // Trigger notification processing if enabled
      if (enableNotificationTrigger) {
        await _triggerLocationBasedNotifications(
          trainNumber: trainNumber,
          date: date,
          response: response,
        );
      }

      return response;
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw Exception('Error occurred while fetching onboarding details: $e');
    }
  }

  static Future<String> _fetchToken() async {
    final userModel = UserModel();
    await userModel.loadUserData();
    return userModel.token;
  }

  /// Trigger location-based notifications when train location data is fetched
  static Future<void> _triggerLocationBasedNotifications({
    required String trainNumber,
    required String date,
    required OnboardingResponse response,
  }) async {
    try {
      // Rate limiting: Check if we've sent notifications recently for this train
      final prefs = await SharedPreferences.getInstance();
      final lastNotificationKey = 'last_notification_${trainNumber}_$date';
      final lastNotificationTime = prefs.getInt(lastNotificationKey) ?? 0;
      final currentTime = DateTime.now().millisecondsSinceEpoch;

      // Rate limit: Only send notifications every 10 minutes for the same train
      const rateLimitMinutes = 10;
      const rateLimitMs = rateLimitMinutes * 60 * 1000;

      if (currentTime - lastNotificationTime < rateLimitMs) {
        if (kDebugMode) {
          print(
              '📍 Location notification: Rate limited for train $trainNumber');
        }
        return;
      }

      // Get current user location for proximity calculations
      final userId = prefs.getString('user_id');
      if (userId == null || userId.isEmpty) {
        if (kDebugMode) {
          print('📍 Location notification: No user ID found');
        }
        return;
      }

      // Get FCM token for notification delivery
      String? fcmToken;
      try {
        fcmToken = await FcmTokenService.getFcmToken();
      } catch (e) {
        if (kDebugMode) {
          print(
              '📍 Location notification: Warning - Could not get FCM token: $e');
        }
      }

      // Call the Cloud Function to process notifications
      final result = await FirebaseCloudFunctionService.callNotifyFunction(
        userId: userId,
        trainNumber: trainNumber,
        date: date,
        lat:
            '0.0', // Will be updated by Cloud Function with actual train location
        lng:
            '0.0', // Will be updated by Cloud Function with actual train location
        fcmToken: fcmToken,
      );

      // Update rate limiting timestamp
      await prefs.setInt(lastNotificationKey, currentTime);

      // Log the result
      final status = result['status'] as String;
      final message = result['message'] as String;

      switch (status) {
        case 'sent':
          if (kDebugMode) {
            print('📍 Location notification: Sent - $message');
          }
          break;
        case 'skipped':
          if (kDebugMode) {
            print('📍 Location notification: Skipped - $message');
          }
          break;
        case 'error':
          if (kDebugMode) {
            print('📍 Location notification: Error - $message');
          }
          break;
        default:
          if (kDebugMode) {
            print(
                '📍 Location notification: Unknown status - $status: $message');
          }
      }
    } catch (e) {
      if (kDebugMode) {
        print('📍 Location notification: Error triggering notifications: $e');
      }
    }
  }
}
