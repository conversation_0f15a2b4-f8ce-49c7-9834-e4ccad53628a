import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../firestore_token_service.dart';
import '../fcm_token_service.dart';

/// Debug service to troubleshoot Firestore token storage issues
class FirestoreDebugService {
  /// Comprehensive debug check for Firestore token storage
  static Future<Map<String, dynamic>> debugTokenStorage() async {
    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'checks': <String, dynamic>{},
      'errors': <String>[],
      'success': false,
    };

    try {
      debugPrint('🔍 Starting Firestore token storage debug...');

      // Check 1: FCM Token Generation
      debugPrint('🔍 Check 1: FCM Token Generation');
      final fcmToken = await FcmTokenService.getFcmToken();
      results['checks']['fcm_token_available'] = fcmToken != null;
      results['checks']['fcm_token_length'] = fcmToken?.length ?? 0;
      results['checks']['fcm_token_valid'] = fcmToken != null ? FcmTokenService.isValidFcmToken(fcmToken) : false;
      
      if (fcmToken != null) {
        debugPrint('✅ FCM Token: ${fcmToken.substring(0, 20)}...');
      } else {
        debugPrint('❌ FCM Token: null');
        results['errors'].add('FCM token is null');
      }

      // Check 2: User ID Availability
      debugPrint('🔍 Check 2: User ID Availability');
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');
      results['checks']['user_id_available'] = userId != null;
      results['checks']['user_id'] = userId ?? 'null';
      
      if (userId != null) {
        debugPrint('✅ User ID: $userId');
      } else {
        debugPrint('❌ User ID: null');
        results['errors'].add('User ID is null');
      }

      // Check 3: Firestore Token Storage
      debugPrint('🔍 Check 3: Firestore Token Storage');
      if (fcmToken != null && userId != null) {
        final storeResult = await FirestoreTokenService.storeTokenInFirestore(
          fcmToken: fcmToken,
          userId: userId,
        );
        results['checks']['firestore_storage_success'] = storeResult;
        
        if (storeResult) {
          debugPrint('✅ Firestore storage: Success');
        } else {
          debugPrint('❌ Firestore storage: Failed');
          results['errors'].add('Firestore storage failed');
        }
      } else {
        results['checks']['firestore_storage_success'] = false;
        results['errors'].add('Cannot test Firestore storage - missing FCM token or user ID');
      }

      // Check 4: Firestore Token Retrieval
      debugPrint('🔍 Check 4: Firestore Token Retrieval');
      if (userId != null) {
        final tokenInfo = await FirestoreTokenService.getTokenInfo(userId: userId);
        results['checks']['firestore_retrieval_success'] = tokenInfo != null;
        results['checks']['retrieved_token_info'] = tokenInfo;
        
        if (tokenInfo != null) {
          debugPrint('✅ Firestore retrieval: Success');
          debugPrint('📱 Retrieved token: ${tokenInfo['fcm_token']?.toString().substring(0, 20)}...');
          debugPrint('🕐 Updated at: ${tokenInfo['updated_at']}');
        } else {
          debugPrint('❌ Firestore retrieval: Failed');
          results['errors'].add('Could not retrieve token from Firestore');
        }
      }

      // Check 5: Token Sync Status
      debugPrint('🔍 Check 5: Token Sync Status');
      final authToken = prefs.getString('authToken');
      if (authToken != null) {
        final syncResult = await FcmTokenService.ensureTokenSynced(authToken);
        results['checks']['token_sync_success'] = syncResult;
        
        if (syncResult) {
          debugPrint('✅ Token sync: Success');
        } else {
          debugPrint('❌ Token sync: Failed');
          results['errors'].add('Token sync failed');
        }
      } else {
        results['checks']['token_sync_success'] = false;
        results['errors'].add('No auth token available for sync');
      }

      // Overall success
      results['success'] = results['errors'].isEmpty;
      
      debugPrint('🔍 Debug complete. Success: ${results['success']}');
      if (results['errors'].isNotEmpty) {
        debugPrint('❌ Errors found: ${results['errors']}');
      }

    } catch (e) {
      debugPrint('❌ Debug error: $e');
      results['errors'].add('Debug process error: $e');
      results['success'] = false;
    }

    return results;
  }

  /// Quick token status check
  static Future<void> quickTokenCheck() async {
    try {
      debugPrint('🔍 Quick token check...');
      
      final fcmToken = await FcmTokenService.getFcmToken();
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');
      
      debugPrint('📱 FCM Token: ${fcmToken != null ? '${fcmToken.substring(0, 20)}...' : 'null'}');
      debugPrint('👤 User ID: ${userId ?? 'null'}');
      
      if (userId != null) {
        final tokenInfo = await FirestoreTokenService.getTokenInfo(userId: userId);
        debugPrint('🔥 Firestore Token: ${tokenInfo?['fcm_token'] != null ? '${tokenInfo!['fcm_token'].toString().substring(0, 20)}...' : 'null'}');
      }
      
    } catch (e) {
      debugPrint('❌ Quick check error: $e');
    }
  }

  /// Force token refresh and storage
  static Future<bool> forceTokenRefresh() async {
    try {
      debugPrint('🔄 Forcing token refresh...');
      
      // Get fresh token
      final freshToken = await FcmTokenService.getFreshFcmToken();
      if (freshToken == null) {
        debugPrint('❌ Could not get fresh token');
        return false;
      }
      
      debugPrint('✅ Fresh token obtained: ${freshToken.substring(0, 20)}...');
      
      // Get user ID
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');
      if (userId == null) {
        debugPrint('❌ No user ID available');
        return false;
      }
      
      // Force store in Firestore
      final storeResult = await FirestoreTokenService.storeTokenInFirestore(
        fcmToken: freshToken,
        userId: userId,
      );
      
      if (storeResult) {
        debugPrint('✅ Token force refresh successful');
        return true;
      } else {
        debugPrint('❌ Token force refresh failed');
        return false;
      }
      
    } catch (e) {
      debugPrint('❌ Force refresh error: $e');
      return false;
    }
  }
}
