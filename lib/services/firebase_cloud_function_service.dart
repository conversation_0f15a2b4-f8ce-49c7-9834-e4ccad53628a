import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

/// Service for calling Firebase Cloud Functions
class FirebaseCloudFunctionService {
  // Firebase Cloud Function endpoint
  static const String _notifyEndpoint = 
      'https://us-central1-railwaysapp-prod.cloudfunctions.net/notify';

  /// Call the /notify Cloud Function endpoint
  /// Returns a map with status information:
  /// - 'status': 'sent', 'skipped', or 'error'
  /// - 'message': descriptive message
  /// - 'details': additional response details (optional)
  static Future<Map<String, dynamic>> callNotifyFunction({
    required String userId,
    required String trainNumber,
    required String date,
    required String lat,
    required String lng,
    String? fcmToken,
  }) async {
    try {
      // Prepare request body
      final Map<String, dynamic> requestBody = {
        'user_id': userId,
        'train_number': trainNumber,
        'date': date,
        'lat': lat,
        'lng': lng,
      };

      // Include FCM token only if provided
      if (fcmToken != null && fcmToken.isNotEmpty) {
        requestBody['fcm_token'] = fcmToken;
      }

      if (kDebugMode) {
        debugPrint('🔔 Calling Firebase Cloud Function /notify');
        debugPrint('📍 Request: $requestBody');
      }

      // Make POST request to Cloud Function
      final response = await http.post(
        Uri.parse(_notifyEndpoint),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      ).timeout(
        const Duration(seconds: 30), // 30 second timeout
        onTimeout: () {
          throw Exception('Cloud Function request timeout');
        },
      );

      if (kDebugMode) {
        debugPrint('📥 Cloud Function Response Status: ${response.statusCode}');
        debugPrint('📥 Cloud Function Response Body: ${response.body}');
      }

      // Parse response
      final Map<String, dynamic> responseData;
      try {
        responseData = jsonDecode(response.body) as Map<String, dynamic>;
      } catch (e) {
        return {
          'status': 'error',
          'message': 'Invalid response format from Cloud Function',
          'details': 'Response: ${response.body}',
        };
      }

      // Handle different response status codes
      if (response.statusCode == 200) {
        // Check if notification was skipped (already sent)
        if (responseData['skipped'] == true) {
          return {
            'status': 'skipped',
            'message': 'Notification already sent for this location',
            'details': responseData,
          };
        } else {
          return {
            'status': 'sent',
            'message': 'Notification sent successfully',
            'details': responseData,
          };
        }
      } else if (response.statusCode == 404) {
        return {
          'status': 'skipped',
          'message': 'User FCM token not found',
          'details': responseData,
        };
      } else if (response.statusCode >= 400 && response.statusCode < 500) {
        return {
          'status': 'error',
          'message': 'Client error: ${responseData['error'] ?? 'Unknown error'}',
          'details': responseData,
        };
      } else if (response.statusCode >= 500) {
        return {
          'status': 'error',
          'message': 'Server error: ${responseData['error'] ?? 'Unknown error'}',
          'details': responseData,
        };
      } else {
        return {
          'status': 'error',
          'message': 'Unexpected response status: ${response.statusCode}',
          'details': responseData,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Firebase Cloud Function Error: $e');
      }
      
      return {
        'status': 'error',
        'message': 'Failed to call Cloud Function: ${e.toString()}',
        'details': null,
      };
    }
  }

  /// Helper method to format current date for API calls
  static String getCurrentDateString() {
    final now = DateTime.now();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  /// Test the Cloud Function endpoint with sample data
  static Future<Map<String, dynamic>> testNotifyFunction() async {
    return await callNotifyFunction(
      userId: 'test_user_${DateTime.now().millisecondsSinceEpoch}',
      trainNumber: '12345',
      date: getCurrentDateString(),
      lat: '28.6139', // New Delhi coordinates
      lng: '77.2090',
    );
  }
}
