import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/authentication_services/jwt_service.dart';

/// Service for managing FCM tokens in Firestore
/// This service stores FCM tokens in Firestore for the Cloud Function to access
class FirestoreTokenService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _tokensCollection = 'tokens';

  /// Store FCM token in Firestore for the current user
  /// Uses user ID from login response stored in SharedPreferences
  static Future<bool> storeTokenInFirestore({
    String? fcmToken,
    String? userId,
  }) async {
    try {
      // Get user ID if not provided
      final String? actualUserId = userId ?? await _getUserId();
      if (actualUserId == null || actualUserId.isEmpty) {
        debugPrint('❌ Cannot store token: No user ID available');
        return false;
      }

      // Get FCM token if not provided
      final String? actualToken =
          fcmToken ?? await FcmTokenService.getFcmToken();
      if (actualToken == null || actualToken.isEmpty) {
        debugPrint('❌ Cannot store token: No FCM token available');
        return false;
      }

      // Get device info for additional context
      final deviceInfo = await FcmTokenService.getDeviceInfo();

      // Prepare token document
      final tokenData = {
        'fcm_token': actualToken,
        'device_info': deviceInfo,
        'updated_at': FieldValue.serverTimestamp(),
        'platform': deviceInfo['platform'] ?? 'unknown',
        'app_version': deviceInfo['app_version'] ?? 'unknown',
      };

      // Store in Firestore
      await _firestore
          .collection(_tokensCollection)
          .doc(actualUserId)
          .set(tokenData, SetOptions(merge: true));

      debugPrint('✅ FCM token stored in Firestore for user: $actualUserId');
      return true;
    } catch (e) {
      debugPrint('❌ Error storing FCM token in Firestore: $e');
      return false;
    }
  }

  /// Get FCM token from Firestore for a specific user
  static Future<String?> getTokenFromFirestore(String userId) async {
    try {
      final doc =
          await _firestore.collection(_tokensCollection).doc(userId).get();

      if (doc.exists) {
        final data = doc.data();
        return data?['fcm_token'] as String?;
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting FCM token from Firestore: $e');
      return null;
    }
  }

  /// Delete FCM token from Firestore (for logout scenarios)
  static Future<bool> deleteTokenFromFirestore({String? userId}) async {
    try {
      final String? actualUserId = userId ?? await _getUserId();
      if (actualUserId == null || actualUserId.isEmpty) {
        debugPrint('❌ Cannot delete token: No user ID available');
        return false;
      }

      await _firestore.collection(_tokensCollection).doc(actualUserId).delete();

      debugPrint('✅ FCM token deleted from Firestore for user: $actualUserId');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting FCM token from Firestore: $e');
      return false;
    }
  }

  /// Update FCM token in Firestore when token refreshes
  static Future<bool> updateTokenInFirestore(String newToken,
      {String? userId}) async {
    try {
      final String? actualUserId = userId ?? await _getUserId();
      if (actualUserId == null || actualUserId.isEmpty) {
        debugPrint('❌ Cannot update token: No user ID available');
        return false;
      }

      await _firestore.collection(_tokensCollection).doc(actualUserId).update({
        'fcm_token': newToken,
        'updated_at': FieldValue.serverTimestamp(),
      });

      debugPrint('✅ FCM token updated in Firestore for user: $actualUserId');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating FCM token in Firestore: $e');
      return false;
    }
  }

  /// Check if user has a token stored in Firestore
  static Future<bool> hasTokenInFirestore({String? userId}) async {
    try {
      final String? actualUserId = userId ?? await _getUserId();
      if (actualUserId == null || actualUserId.isEmpty) {
        return false;
      }

      final doc = await _firestore
          .collection(_tokensCollection)
          .doc(actualUserId)
          .get();

      return doc.exists && doc.data()?['fcm_token'] != null;
    } catch (e) {
      debugPrint('❌ Error checking token in Firestore: $e');
      return false;
    }
  }

  /// Initialize Firestore token storage for the current user
  /// This should be called after successful login
  static Future<bool> initializeForUser({String? userId}) async {
    try {
      final String? actualUserId = userId ?? await _getUserId();
      if (actualUserId == null || actualUserId.isEmpty) {
        debugPrint('❌ Cannot initialize: No user ID available');
        return false;
      }

      // Get fresh FCM token
      final fcmToken = await FcmTokenService.getFreshFcmToken();
      if (fcmToken == null) {
        debugPrint('❌ Cannot initialize: No FCM token available');
        return false;
      }

      // Store in Firestore
      return await storeTokenInFirestore(
        fcmToken: fcmToken,
        userId: actualUserId,
      );
    } catch (e) {
      debugPrint('❌ Error initializing Firestore token service: $e');
      return false;
    }
  }

  /// Get user ID from SharedPreferences (updated to use JWT-extracted user ID)
  static Future<String?> _getUserId() async {
    try {
      // First try to get JWT-extracted user ID (most reliable)
      final jwtUserId = await JwtService.getStoredUserId();
      if (jwtUserId != null) {
        return jwtUserId;
      }

      final prefs = await SharedPreferences.getInstance();

      // Fallback: try to get user ID from login response
      final loginResponseJson = prefs.getString('loginResponse');
      if (loginResponseJson != null) {
        final loginData = json.decode(loginResponseJson);
        // Check various possible fields for user ID
        return loginData['user_id']?.toString() ??
            loginData['id']?.toString() ??
            loginData['userId']?.toString();
      }

      // Last fallback: try direct user_id key
      return prefs.getString('user_id');
    } catch (e) {
      debugPrint('❌ Error getting user ID: $e');
      return null;
    }
  }

  /// Sync local FCM token with Firestore
  /// This ensures the Cloud Function can find the token
  static Future<bool> syncWithFirestore() async {
    try {
      final userId = await _getUserId();
      if (userId == null) {
        debugPrint('❌ Cannot sync: No user ID available');
        return false;
      }

      final localToken = await FcmTokenService.getFcmToken();
      if (localToken == null) {
        debugPrint('❌ Cannot sync: No local FCM token available');
        return false;
      }

      // Check if token exists in Firestore
      final firestoreToken = await getTokenFromFirestore(userId);

      if (firestoreToken == null || firestoreToken != localToken) {
        // Token doesn't exist or is different, store the current one
        return await storeTokenInFirestore(
          fcmToken: localToken,
          userId: userId,
        );
      }

      debugPrint('✅ FCM token already synced with Firestore');
      return true;
    } catch (e) {
      debugPrint('❌ Error syncing with Firestore: $e');
      return false;
    }
  }

  /// Get token info from Firestore (for debugging)
  static Future<Map<String, dynamic>?> getTokenInfo({String? userId}) async {
    try {
      final String? actualUserId = userId ?? await _getUserId();
      if (actualUserId == null || actualUserId.isEmpty) {
        return null;
      }

      final doc = await _firestore
          .collection(_tokensCollection)
          .doc(actualUserId)
          .get();

      if (doc.exists) {
        final data = doc.data();
        return {
          'user_id': actualUserId,
          'fcm_token': data?['fcm_token'],
          'platform': data?['platform'],
          'app_version': data?['app_version'],
          'updated_at': data?['updated_at'],
          'device_info': data?['device_info'],
        };
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting token info from Firestore: $e');
      return null;
    }
  }

  /// Test Firestore connection and permissions
  static Future<bool> testFirestoreConnection() async {
    try {
      final userId = await _getUserId();
      if (userId == null) {
        debugPrint('❌ Test failed: No user ID available');
        return false;
      }

      // Try to read the user's token document
      final doc =
          await _firestore.collection(_tokensCollection).doc(userId).get();

      debugPrint('✅ Firestore connection test successful');
      debugPrint('📄 Document exists: ${doc.exists}');

      if (doc.exists) {
        final data = doc.data();
        debugPrint('📄 Token exists: ${data?['fcm_token'] != null}');
      }

      return true;
    } catch (e) {
      debugPrint('❌ Firestore connection test failed: $e');
      return false;
    }
  }
}
