/// Enumeration for notification status tracking
enum NotificationStatus {
  unread,
  read,
  cleared,
}

/// Enumeration for notification types
enum NotificationType {
  general,
  trainLocationUpdate,
  enhancedTrainLocationUpdate,
  onboarding,
  offBoarding,
  stationApproach,
  coachUpdate,
  boardingAlert,
  offBoardingAlert,
  proximityAlert,
  coachSpecific,
}

/// Enhanced notification model with status tracking and type categorization
class NotificationModel {
  final String? id;
  final String? title;
  final String? body;
  final Map<String, dynamic> data;
  final int timestamp;
  final bool isRead;
  final bool isCleared;
  final NotificationStatus status;
  final NotificationType type;

  NotificationModel({
    this.id,
    this.title,
    this.body,
    required this.data,
    required this.timestamp,
    this.isRead = false,
    this.isCleared = false,
    this.status = NotificationStatus.unread,
    this.type = NotificationType.general,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    // Determine notification type from data
    NotificationType notificationType = NotificationType.general;
    if (json['data'] != null) {
      final dataType = json['data']['type'] as String?;
      switch (dataType) {
        case 'train_location_update':
          notificationType = NotificationType.trainLocationUpdate;
          break;
        case 'enhanced_train_location_update':
          notificationType = NotificationType.enhancedTrainLocationUpdate;
          break;
        case 'onboarding':
          notificationType = NotificationType.onboarding;
          break;
        case 'off_boarding':
          notificationType = NotificationType.offBoarding;
          break;
        case 'station_approach':
          notificationType = NotificationType.stationApproach;
          break;
        case 'coach_update':
          notificationType = NotificationType.coachUpdate;
          break;
        default:
          notificationType = NotificationType.general;
      }
    }

    // Determine status from isRead and isCleared
    NotificationStatus status = NotificationStatus.unread;
    final isRead = json['isRead'] ?? false;
    final isCleared = json['isCleared'] ?? false;

    if (isCleared) {
      status = NotificationStatus.cleared;
    } else if (isRead) {
      status = NotificationStatus.read;
    } else {
      status = NotificationStatus.unread;
    }

    return NotificationModel(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      data: json['data'] ?? {},
      timestamp: json['timestamp'],
      isRead: isRead,
      isCleared: isCleared,
      status: status,
      type: notificationType,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'data': data,
      'timestamp': timestamp,
      'isRead': isRead,
      'isCleared': isCleared,
      'status': status.name,
      'type': type.name,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    Map<String, dynamic>? data,
    int? timestamp,
    bool? isRead,
    bool? isCleared,
    NotificationStatus? status,
    NotificationType? type,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      isCleared: isCleared ?? this.isCleared,
      status: status ?? this.status,
      type: type ?? this.type,
    );
  }

  /// Helper method to check if this is an enhanced train location notification
  bool get isEnhancedTrainLocationUpdate =>
      type == NotificationType.enhancedTrainLocationUpdate;

  /// Helper method to get station codes from enhanced notifications
  List<String> get stationCodes {
    if (data['coach_data'] != null) {
      try {
        // For now, we'll use the station_code field directly
        // In the future, this could parse the JSON coach_data string
        if (data['station_code'] != null) {
          final stationCode = data['station_code'] as String;
          return stationCode
              .split(', '); // Handle comma-separated station codes
        }
        return [];
      } catch (e) {
        return [];
      }
    }

    // Fallback: try to get station code from the data directly
    if (data['station_code'] != null) {
      final stationCode = data['station_code'] as String;
      return stationCode.split(', '); // Handle comma-separated station codes
    }

    return [];
  }

  /// Helper method to format notification body for table display
  String get formattedBody {
    if (isEnhancedTrainLocationUpdate && body != null) {
      // The body already contains the formatted table from the Cloud Function
      return body!;
    }
    return body ?? '';
  }
}
