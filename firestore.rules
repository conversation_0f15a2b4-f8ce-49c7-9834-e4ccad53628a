rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // FCM tokens collection - only accessible by authenticated users for their own tokens
    match /tokens/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Sent alerts collection - only accessible by Cloud Functions
    match /sentAlerts/{alertId} {
      allow read, write: if false; // Only Cloud Functions can access this
    }
    
    // Default rule - deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
